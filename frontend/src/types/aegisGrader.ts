// --- Unified Status System ---
// Single status enum that matches backend exactly
export enum Status {
    PENDING = 'pending',
    PROCESSING = 'processing',
    EVALUATING = 'evaluating',
    COMPLETED = 'completed',
    ERROR = 'error',
    PARTIAL_COMPLETION = 'partial_completion'
}

// Type alias for backward compatibility
export type GradingStatus = Status;
export type AnswerSheetStatus = Status;
export type OverallStatus = Status;

// Utility functions for status handling
export const StatusUtils = {
    // Normalize any status value to our standard format
    normalize: (status: string | Status | undefined): Status => {
        if (!status) return Status.PENDING;

        const normalized = status.toString().toLowerCase().replace(/\s+/g, '_');

        switch (normalized) {
            case 'completed':
            case 'complete':
                return Status.COMPLETED;
            case 'processing':
            case 'in_progress':
            case 'in progress':
            case 'grading':
                return Status.PROCESSING;
            case 'evaluating':
                return Status.EVALUATING;
            case 'error':
            case 'failed':
            case 'failure':
                return Status.ERROR;
            case 'partial completion':
            case 'partial_completion':
                return Status.PARTIAL_COMPLETION;
            case 'pending':
            default:
                return Status.PENDING;
        }
    },

    // Get display text for status
    getDisplayText: (status: string | Status | undefined): string => {
        const normalized = StatusUtils.normalize(status);
        switch (normalized) {
            case Status.COMPLETED:
                return 'Completed';
            case Status.PROCESSING:
                return 'Processing';
            case Status.EVALUATING:
                return 'Evaluating';
            case Status.ERROR:
                return 'Failed';
            case Status.PARTIAL_COMPLETION:
                return 'Partial Completion';
            case Status.PENDING:
            default:
                return 'Pending';
        }
    },

    // Get CSS classes for status
    getStatusClasses: (status: string | Status | undefined): string => {
        const normalized = StatusUtils.normalize(status);
        switch (normalized) {
            case Status.COMPLETED:
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            case Status.PROCESSING:
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case Status.EVALUATING:
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
            case Status.ERROR:
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            case Status.PARTIAL_COMPLETION:
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
            case Status.PENDING:
            default:
                return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400';
        }
    }
};

export interface ScannedPage {
    imageUrl: string; // dataURL
    timestamp: number;
}

export interface TestDetails {
    createdBy: string;
    className: string;
    subject: string;
    date: string; // YYYY-MM-DD
}

export interface TestDocument {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl: string; // Original filename
    timestamp: number;
    // pdfData?: string; // Base64 dataURL - Optional initially
    pdfData?: File // uploaded file object
    className: string;
    uploading?: boolean;
    error?: string; // Add error field per document
    evaluationResult?: {
        evaluation: {
            total_marks: string;
            maximum_possible_marks: string;
            percentage_score: string;
            section: Array<{
                name: string;
                section_marks: string;
                section_possible_marks: string;
                question: Array<{
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                }> | {
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                };
            }>;
        };
    };
    // New fields to match backend answerSheets structure
    status?: Status;
    processedAt?: Date;
}

// New interface for processing statistics
export interface ProcessingStats {
    totalAnswerSheets: number;
    successfulEvaluations: number;
    failedEvaluations: number;
    completedAt?: Date;
    processingStartedAt?: Date;
    overallStatus: Status;
}

// New interface for credit information
export interface CreditInfo {
    totalCreditsCharged: number;
    creditsRefunded: number;
    originalTransactionId?: string;
    refundTransactionIds: string[];
}

export interface RubricDocument {
    type: 'rubric' | 'questionPaper';
    pdfUrl: string; // Original filename
    // pdfData: string; // Base64 dataURL
    pdfData: File // uploaded file object
    timestamp: number;
}

export interface TestSubmission {
    id?: string;
    testDetails: TestDetails;
    answerSheets: TestDocument[]; // Should contain pdfData for submission
    questionPaper?: RubricDocument;
    rubric?: RubricDocument;
    status?: Status; // Unified status field
    results?: any; // To store grading results from backend
    // New fields to match backend model
    processingStats?: ProcessingStats;
    creditInfo?: CreditInfo;
    createdAt?: Date;
    updatedAt?: Date;
}
