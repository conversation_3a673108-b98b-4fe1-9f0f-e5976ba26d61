import { EVALUATION_CONFIG, CONSTANTS } from '../../config/evaluationConfig';

// Test data with the new XML format
const newFormatEvaluationData = [
    `"<evaluation>\\n    <total_marks_awarded>100</total_marks_awarded>\\n    <maximum_possible_marks>250</maximum_possible_marks>\\n\\n    <question number=\\"1\\">\\n        <marks_awarded>5</marks_awarded>\\n        <marks_possible>10</marks_possible>\\n        <detailed_feedback>\\n            <overall_comment>The answer attempts to address the question.</overall_comment>\\n        </detailed_feedback>\\n        <marks_breakdown>\\n            <criterion name=\\"Understanding\\">1/3</criterion>\\n            <criterion name=\\"Accuracy\\">2/3</criterion>\\n        </marks_breakdown>\\n    </question>\\n</evaluation>"`
];

const oldFormatEvaluationData = [
    `<evaluation>
    <total_marks>100</total_marks>
    <maximum_possible_marks>250</maximum_possible_marks>
    <percentage_score>40</percentage_score>
    <section>
        <name>Main Section</name>
        <section_marks>100</section_marks>
        <section_possible_marks>250</section_possible_marks>
        <question>
            <number>1</number>
            <marks_awarded>5</marks_awarded>
            <marks_possible>10</marks_possible>
            <feedback>Good attempt</feedback>
        </question>
    </section>
</evaluation>`
];

describe('GradingDetails Parsing Tests', () => {
    test('should parse new XML format correctly', () => {
        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(newFormatEvaluationData);
        
        expect(result).not.toBeNull();
        expect(result?.total_marks).toBe('100');
        expect(result?.maximum_possible_marks).toBe('250');
        expect(result?.section).toHaveLength(1);
        expect(result?.section[0].question).toHaveLength(1);
        expect(result?.section[0].question[0].marks_awarded).toBe('5');
        expect(result?.section[0].question[0].marks_possible).toBe('10');
    });

    test('should parse old XML format correctly', () => {
        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(oldFormatEvaluationData);
        
        expect(result).not.toBeNull();
        expect(result?.total_marks).toBe('100');
        expect(result?.maximum_possible_marks).toBe('250');
        expect(result?.percentage_score).toBe('40');
        expect(result?.section).toHaveLength(1);
        expect(result?.section[0].question).toHaveLength(1);
    });

    test('should handle escaped JSON format', () => {
        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(newFormatEvaluationData);
        
        expect(result).not.toBeNull();
        expect(result?.total_marks).toBe('100');
    });

    test('should handle missing sections by creating default section', () => {
        const directQuestionData = [
            `<evaluation>
            <total_marks_awarded>50</total_marks_awarded>
            <maximum_possible_marks>100</maximum_possible_marks>
            <question number="1">
                <marks_awarded>25</marks_awarded>
                <possible_marks>50</possible_marks>
                <detailed_feedback>
                    <overall_comment>Good work</overall_comment>
                </detailed_feedback>
            </question>
            <question number="2">
                <marks_awarded>25</marks_awarded>
                <possible_marks>50</possible_marks>
                <feedback>Excellent</feedback>
            </question>
        </evaluation>`
        ];

        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(directQuestionData);
        
        expect(result).not.toBeNull();
        expect(result?.section).toHaveLength(1);
        expect(result?.section[0].name).toBe('Main Section');
        expect(result?.section[0].question).toHaveLength(2);
        expect(result?.section[0].question[0]).toBe('1');
        expect(result?.section[0].question[1]).toBe('2');
    });

    test('should return null for invalid data', () => {
        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        
        expect(parser.parseEvaluation(null)).toBeNull();
        expect(parser.parseEvaluation([])).toBeNull();
        expect(parser.parseEvaluation(['invalid xml'])).toBeNull();
    });

    test('should handle different marks tag variations', () => {
        const variationsData = [
            `<evaluation>
            <total_marks_awarded>30</total_marks_awarded>
            <possible_marks>60</possible_marks>
            <question number="1">
                <marks_awarded>10</marks_awarded>
                <marks_possible>20</marks_possible>
            </question>
            <question number="2">
                <awarded>10</awarded>
                <possible_marks>20</possible_marks>
            </question>
            <question number="3">
                <score>10</score>
                <total>20</total>
            </question>
        </evaluation>`
        ];

        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(variationsData);
        
        expect(result).not.toBeNull();
        expect(result?.total_marks).toBe('30');
        expect(result?.maximum_possible_marks).toBe('60');
        expect(result?.section[0].question).toHaveLength(3);
        
        // All questions should have their marks parsed correctly
        expect(result?.section[0].question[0].marks_awarded).toBe('10');
        expect(result?.section[0].question[1].marks_awarded).toBe('10');
        expect(result?.section[0].question[2].marks_awarded).toBe('10');
    });

    test('should handle criterion tag mismatch errors', () => {
        // This test specifically addresses the error: "Opening and ending tag mismatch: criterion line X and question"
        const problematicXML = [
            `<evaluation>
                <total_marks_awarded>145</total_marks_awarded>
                <maximum_possible_marks>250</maximum_possible_marks>

                <question number="1">
                    <marks_awarded>4</marks_awarded>
                    <marks_possible>10</marks_possible>
                    <detailed_feedback>
                        <overall_comment>The answer attempts to address the question but lacks depth.</overall_comment>
                    </detailed_feedback>
                    <marks_breakdown>
                        <criterion name="Understanding">1/3
                        <criterion name="Analysis">2/4
                        <criterion name="Examples">1/3
                    </marks_breakdown>
                </question>
            </evaluation>`
        ];

        const parser = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
        const result = parser.parseEvaluation(problematicXML);

        // Should successfully parse despite malformed criterion tags
        expect(result).not.toBeNull();
        expect(result?.total_marks).toBe('145');
        expect(result?.maximum_possible_marks).toBe('250');
        expect(result?.section).toHaveLength(1);
        expect(result?.section[0].question).toHaveLength(1);
        expect(result?.section[0].question[0].marks_awarded).toBe('4');
        expect(result?.section[0].question[0].marks_possible).toBe('10');
    });
});
