import React, { useState, useRef, useEffect, useCallback } from "react";
import axios from 'axios';
import { CameraIcon, SparklesIcon, XMarkIcon, DocumentArrowUpIcon, TrashIcon, CheckCircleIcon as CheckCircleSolid, ArrowPathIcon, EyeIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import { jsPDF } from 'jspdf';
import { useAxiosPrivate } from "@/hooks/useAxiosPrivate";

import { ScannedPage, TestDocument, TestDetails, RubricDocument } from "@/types/aegisGrader";
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import { useUser } from '../contexts/userContext';
import CreditBalance from '@/components/credits/CreditBalance';
import CreditPurchaseModal from '@/components/credits/CreditPurchaseModal';
import { createPortal } from "react-dom";
import { toast, ToastContainer } from "react-toastify";
import { usePageRefresh } from "@/hooks/usePageRefresh";
import { useMobileInteractions, useMobileKeyboard } from "@/hooks/useMobileInteractions";
import PulsatingDots from "@/components/PulsatingDotsLoader";
import DocumentPreview from "@/components/DocumentPreview";

// --- Constants ---
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const FILE_SIZE_ERROR = `File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)}MB. Please upload a smaller file.`;
const FILE_READ_TIMEOUT = 30000; // 30 seconds

const subjects = [
    "Paper A: Compulsory Indian Language",
    "Paper B: Compulsory English",
    "General Studies Paper I",
    "General Studies Paper II",
    "General Studies Paper III",
    "General Studies Paper IV",
    "Essay",
    "Optional Subject"
]

// --- Reusable Input Component ---
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label?: string;
}
const Input: React.FC<InputProps> = ({ label, id, className, ...props }) => (
    <div>
        {label && <label htmlFor={id} className="block text-sm font-medium text-muted-foreground mb-1">{label}</label>}
        <input
            id={id}
            className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
            {...props}
        />
    </div>
);

// Tour steps for AegisGrader
// const tourSteps: TourStep[] = [
//     {
//         target: '[data-tour="test-details"]',
//         content: 'Start by entering your test details including class name, subject, and date. This information will be used to organize your grading submissions.',
//         title: 'Step 1: Test Details',
//         placement: 'bottom',
//     },
//     {
//         target: '[data-tour="question-paper"]',
//         content: 'Upload your question paper PDF here. This helps the AI understand the test structure and provides context for accurate grading.',
//         title: 'Step 2: Question Paper',
//         placement: 'bottom',
//     },
//     {
//         target: '[data-tour="rubric"]',
//         content: 'Upload your grading rubric to ensure consistent and accurate scoring. The AI will use this to grade student responses.',
//         title: 'Step 3: Grading Rubric',
//         placement: 'bottom',
//     },
//     {
//         target: '[data-tour="answer-sheets"]',
//         content: 'Upload student answer sheets by scanning with your camera, uploading PDFs, or dragging and dropping files. You can process multiple sheets at once.',
//         title: 'Step 4: Answer Sheets',
//         placement: 'top',
//     },
//     {
//         target: '[data-tour="submit-grading"]',
//         content: 'Once all documents are uploaded, click here to submit for AI grading. The process typically takes a few minutes depending on the number of sheets.',
//         title: 'Step 5: Submit for Grading',
//         placement: 'top',
//     },
//     {
//         target: '[data-tour="view-submissions"]',
//         content: 'After grading is complete, view all your submissions here. You can track progress and access detailed results for each test.',
//         title: 'Step 6: View Results',
//         placement: 'top',
//     },
// ];

// --- Reusable File Input Label ---
interface FileInputLabelProps {
    htmlFor: string;
    icon: React.ElementType;
    text: string;
    uploadedFile?: string;
    isLoading?: boolean;
}
const FileInputLabel: React.FC<FileInputLabelProps> = ({ htmlFor, icon: Icon, text, uploadedFile, isLoading }) => (
    <label htmlFor={htmlFor} className={`flex flex-col items-center justify-center gap-1 sm:gap-2 p-2 sm:p-3 xl:p-4 min-h-[100px] sm:min-h-[120px] xl:min-h-[140px] border-2 border-dashed rounded-lg cursor-pointer transition-colors touch-manipulation ${uploadedFile ? 'border-primary/50 bg-primary/5 hover:border-primary/70' : 'border-border hover:border-primary/50 bg-background hover:bg-accent/10'}`}>
        {isLoading ? (
            <>
                <ArrowPathIcon className="w-4 h-4 sm:w-5 sm:h-5 xl:w-6 xl:h-6 text-muted-foreground animate-spin" />
                <span className="text-[10px] sm:text-xs text-muted-foreground">Loading...</span>
            </>
        ) : uploadedFile ? (
            <>
                <CheckCircleSolid className="w-4 h-4 sm:w-5 sm:h-5 xl:w-6 xl:h-6 text-success" />
                <span className="text-[10px] sm:text-xs text-center text-muted-foreground truncate w-full px-1" title={uploadedFile}>{uploadedFile}</span>
                <span className="text-[9px] sm:text-[10px] xl:text-xs text-primary hover:underline">(Replace)</span>
            </>
        ) : (
            <>
                <Icon className="w-5 h-5 sm:w-6 sm:h-6 xl:w-8 xl:h-8 text-muted-foreground/70" />
                <span className="text-[10px] sm:text-xs xl:text-sm text-muted-foreground text-center px-1 sm:px-2 leading-tight">{text}</span>
            </>
        )}
    </label>
);

// --- Main Component ---
export const AegisGrader: React.FC = () => {
    const { user } = useUser();
    const [isScanning, setIsScanning] = useState(false);
    const [scannedPages, setScannedPages] = useState<ScannedPage[]>([]);
    const [studentName, setStudentName] = useState("");
    const [rollNumber, setRollNumber] = useState("");

    const [cameraLoading, setCameraLoading] = useState(false);
    const [testDetails, setTestDetails] = useState<TestDetails>({
        createdBy: user?.id || '',
        className: '',
        subject: '',
        date: new Date().toISOString().split('T')[0]
    });
    const [testDocuments, setTestDocuments] = useState<TestDocument[]>([]);
    const [rubricDocuments, setRubricDocuments] = useState<RubricDocument[]>([]);
    const [selectedDocumentPreview, setSelectedDocumentPreview] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState<Record<string, boolean>>({});
    const [debugInfo, setDebugInfo] = useState<string[]>([]); // Used for debugging camera issues
    // eslint-disable-next-line @typescript-eslint/no-unused-vars

    const videoRef = useRef<HTMLVideoElement>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const questionPaperInputRef = useRef<HTMLInputElement>(null);
    const rubricInputRef = useRef<HTMLInputElement>(null);
    const answerSheetInputRef = useRef<HTMLInputElement>(null);
    const axiosPrivate = useAxiosPrivate();

    const [showCreditPurchaseModal, setShowCreditPurchaseModal] = useState(false);
    const [creditBalance, setCreditBalance] = useState<number | null>(null);
    usePageRefresh();

    // Mobile interactions
    const { handleButtonPress } = useMobileInteractions({
        enableSwipe: false, // Disable swipe for this page as it might interfere with camera
        preventScroll: false
    });
    useMobileKeyboard();

    // Update createdBy when user changes
    useEffect(() => {
        if (user?.id) {
            setTestDetails(prev => ({
                ...prev,
                createdBy: user.id || ''
            }));
        }
    }, [user?.id]);

    useEffect(() => {
        return () => {
            streamRef.current?.getTracks().forEach(track => track.stop());
        };
    }, []);

    // Fetch initial credit balance
    useEffect(() => {
        const fetchCreditBalance = async () => {
            try {
                const response = await axiosPrivate.get('/api/credits/balance');
                if (response.data?.data?.currentBalance !== undefined) {
                    setCreditBalance(response.data.data.currentBalance);
                }
            } catch (error) {
                console.error('Error fetching initial credit balance:', error);
                // Don't show error toast here as CreditBalance component will handle it
            }
        };

        fetchCreditBalance();
    }, [axiosPrivate]);

    const addDebug = (message: string) => {
        console.error(`DEBUG: ${message}`);
        setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    const startScanning = useCallback(async () => {
        setDebugInfo([]);
        setCameraLoading(true);
        setIsScanning(true);
        addDebug("Attempting to start scanning...");

        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        if (videoRef.current) videoRef.current.srcObject = null;

        const constraints = { video: { facingMode: 'environment', width: { ideal: 1280 }, height: { ideal: 720 } } };

        try {
            addDebug("Requesting media devices...");
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            addDebug("Stream obtained.");
            if (!videoRef.current) {
                addDebug("Video ref is null after getting stream.");
                console.error("Video element not ready.");
                return;
            }

            videoRef.current.srcObject = stream;
            streamRef.current = stream;

            videoRef.current.onloadedmetadata = () => addDebug("Video metadata loaded");
            videoRef.current.onloadeddata = () => addDebug("Video data loaded");
            videoRef.current.onplaying = () => { addDebug("Video playing"); setCameraLoading(false); };
            videoRef.current.onwaiting = () => addDebug("Video is waiting");
            videoRef.current.onerror = () => addDebug(`Video error: ${videoRef.current?.error?.message || 'Unknown'}`);

            addDebug("Attempting to play video...");
            await videoRef.current.play();

        } catch (err) {
            addDebug(`Camera access error: ${(err as Error).message}`);
            console.error("Camera access error:", err);
            const errorMsg = `Camera error: ${(err as Error).message}. Ensure permissions are granted and camera is not in use.`;
            toast.error(errorMsg, {
                autoClose: 5000
            });
            setIsScanning(false);
            setCameraLoading(false);
        }
    }, []);

    const capturePage = useCallback(() => {
        addDebug("Attempting capture...");
        if (videoRef.current?.readyState === 4) {
            const canvas = document.createElement("canvas");
            canvas.width = videoRef.current.videoWidth;
            canvas.height = videoRef.current.videoHeight;
            const ctx = canvas.getContext("2d");
            if (ctx) {
                ctx.drawImage(videoRef.current, 0, 0);
                const imageUrl = canvas.toDataURL("image/webp", 0.9);
                setScannedPages(prev => [...prev, { imageUrl, timestamp: Date.now() }]);
                addDebug(`Page captured. Total pages: ${scannedPages.length + 1}`);
            } else {
                addDebug("Failed to get 2D context from canvas.");
                toast.error("Failed to get canvas context for capture.", {
                    autoClose: 3000
                });
            }
        } else {
            addDebug(`Video not ready for capture. State: ${videoRef.current?.readyState}`);
            toast.error("Video not ready. Please wait.", {
                autoClose: 3000
            });
        }
    }, [scannedPages.length]);

    const stopScanning = useCallback(() => {
        addDebug("Stopping scanning...");
        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
        setIsScanning(false);
        setCameraLoading(false);
        if (videoRef.current) {
            videoRef.current.srcObject = null;
            videoRef.current.removeAttribute('src');
            videoRef.current.load();
        }
        addDebug("Scanning stopped.");
    }, []);

    const addImageToPdf = useCallback((pdf: jsPDF, imageUrl: string, pageWidth: number, pageHeight: number): Promise<void> => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                try {
                    const margin = 10;
                    const maxWidth = pageWidth - (margin * 2);
                    const maxHeight = pageHeight - (margin * 2);
                    const imgRatio = img.width / img.height;
                    let imgWidth = maxWidth;
                    let imgHeight = imgWidth / imgRatio;

                    if (imgHeight > maxHeight) {
                        imgHeight = maxHeight;
                        imgWidth = imgHeight * imgRatio;
                    }
                    const x = (pageWidth - imgWidth) / 2;
                    const y = (pageHeight - imgHeight) / 2;
                    pdf.addImage(imageUrl, 'WEBP', x, y, imgWidth, imgHeight);
                    resolve();
                } catch (err) { reject(err); }
            };
            img.onerror = () => { reject(new Error("Failed to load image for PDF")); };
            img.src = imageUrl;
        });
    }, []);

    const generatePdfFromScannedPages = useCallback(async (sName: string, rNum: string, pages: ScannedPage[]): Promise<{ filename: string, pdfData: string }> => {
        if (pages.length === 0) console.error("No scanned pages.");

        const pdf = new jsPDF('p', 'mm', 'a4');
        pdf.setProperties({ title: `${sName} - ${rNum} Test`, subject: 'Scanned Test', author: 'AegisGrader' });

        pdf.setFontSize(22); pdf.text('Scanned Test Document', 105, 30, { align: 'center' });
        pdf.setFontSize(16);
        pdf.text(`Student: ${sName}`, 105, 50, { align: 'center' });
        pdf.text(`Roll Number: ${rNum}`, 105, 60, { align: 'center' });
        pdf.text(`Date: ${new Date().toLocaleDateString()}`, 105, 70, { align: 'center' });
        pdf.text(`Total Pages: ${pages.length}`, 105, 80, { align: 'center' });

        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        for (let i = 0; i < pages.length; i++) {
            pdf.addPage();
            try {
                await addImageToPdf(pdf, pages[i].imageUrl, pageWidth, pageHeight);
                pdf.setFontSize(10);
                pdf.text(`Page ${i + 1} of ${pages.length}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
            } catch (err) {
                console.error(`Error adding image ${i + 1} to PDF:`, err);
                pdf.setFontSize(12); pdf.setTextColor(255, 0, 0);
                pdf.text(`Error loading page ${i + 1}`, 20, 20);
                pdf.setTextColor(0, 0, 0);
            }
        }

        const pdfData = pdf.output('datauristring');
        const filename = `${sName.replace(/\s+/g, '_')}_${rNum}_${Date.now()}.pdf`;
        return { filename, pdfData };
    }, [addImageToPdf]);

    const finishScanning = useCallback(async () => {
        addDebug("Finish scanning button clicked.");
        if (!studentName || !rollNumber) {
            toast.error("Please enter Student Name and Roll Number before finishing.", {
                autoClose: 4000
            });
            return;
        }
        if (scannedPages.length === 0) {
            toast.error("No pages have been scanned.", {
                autoClose: 4000
            });
            return;
        }

        stopScanning();
        setIsUploading(prev => ({ ...prev, scannedPdf: true }));

        try {
            addDebug(`Generating PDF for ${studentName}, ${scannedPages.length} pages...`);
            const { filename, pdfData } = await generatePdfFromScannedPages(studentName, rollNumber, scannedPages);
            addDebug(`PDF generated: ${filename}`);

            // Convert pdfData string to File object for consistency
            const pdfBlob = await fetch(pdfData).then(r => r.blob());
            const pdfFile = new File([pdfBlob], filename, { type: 'application/pdf' });

            const newDoc: TestDocument = {
                id: `test-${Date.now()}`,
                studentName,
                rollNumber,
                pdfUrl: filename,
                pdfData: pdfFile,
                className: testDetails.className,
                timestamp: Date.now(),
                uploading: false
            };

            setTestDocuments(prev => [...prev, newDoc]);
            addDebug(`Document added to list: ${newDoc.id}`);
            toast.success(`PDF created successfully for ${studentName}! (${scannedPages.length} pages)`, {
                autoClose: 5000
            });
            resetForm();

        } catch (err) {
            addDebug(`Error generating PDF: ${(err as Error).message}`);
            console.error("Error generating or saving PDF:", err);
            const errorMsg = `Failed to create PDF: ${(err as Error).message}`;
            toast.error(errorMsg, {
                autoClose: 5000
            });
        } finally {
            setIsUploading(prev => ({ ...prev, scannedPdf: false }));
        }
    }, [studentName, rollNumber, scannedPages, testDetails.className, generatePdfFromScannedPages, stopScanning]);

    const resetForm = () => {
        setStudentName("");
        setRollNumber("");
        setScannedPages([]);
        addDebug("Form reset.");
    };

    const readFileAsDataURL = useCallback((file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            let timeoutId: ReturnType<typeof setTimeout> | null = null;
            const cleanup = () => { if (timeoutId) clearTimeout(timeoutId); reader.onload = reader.onerror = reader.onabort = reader.onprogress = null; };
            timeoutId = setTimeout(() => { cleanup(); reader.abort(); reject(new Error(`File reading timed out (${FILE_READ_TIMEOUT / 1000}s).`)); }, FILE_READ_TIMEOUT);
            reader.onload = () => { cleanup(); const result = reader.result as string; if (result && result.startsWith('data:')) { resolve(result); } else { reject(new Error("FileReader returned invalid result.")); } };
            reader.onerror = () => { cleanup(); reject(new Error(`FileReader error: ${reader.error?.message || 'Read error'}`)); };
            reader.onabort = () => { cleanup(); console.error("Read aborted"); };
            reader.readAsDataURL(file);
        });
    }, []);

    const handleRemoveAnswerSheet = useCallback((docId: string) => {
        setTestDocuments(prev => prev.filter(doc => doc.id !== docId));
    }, []);

    const handleDocumentUpload = useCallback((type: 'rubric' | 'questionPaper') => async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const inputRef = event.target;
        if (!file) return;

        if (file.size > MAX_FILE_SIZE) {
            toast.error(FILE_SIZE_ERROR, {
                autoClose: 5000
            });
            if (inputRef) inputRef.value = '';
            return;
        }
        const tempId = `${type}-${Date.now()}`;
        setIsUploading(prev => ({ ...prev, [tempId]: true }));

        try {
            // const pdfData = await readFileAsDataURL(file);
            const newDoc: RubricDocument = {
                type,
                pdfUrl: file.name,
                pdfData: file,
                timestamp: Date.now()
            };
            // Replace existing doc of the same type, or add new
            setRubricDocuments(prev => [...prev.filter(doc => doc.type !== type), newDoc]);
            toast.success(`${type === 'questionPaper' ? 'Question Paper' : 'Rubric'} uploaded successfully!`, {
                autoClose: 5000
            });
        } catch (err) {
            console.error(`Error reading ${type} file:`, err);
            const errorMsg = `Failed to read ${file.name}: ${(err as Error).message}`;
            toast.error(errorMsg, {
                autoClose: 5000
            });
        } finally {
            setIsUploading(prev => ({ ...prev, [tempId]: false }));
            // Keep file selected in input visually until successful upload (optional based on UX pref)
            // if (inputRef) inputRef.value = '';
        }
    }, [readFileAsDataURL]);

    const handleAnswerSheetUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const inputRef = event.target;
        if (!file) return;

        if (!studentName || !rollNumber) {
            toast.error("Please enter student name and roll number first.", {
                autoClose: 4000
            });
            if (inputRef) inputRef.value = '';
            return;
        }

        if (file.size > MAX_FILE_SIZE) {
            toast.error(FILE_SIZE_ERROR, {
                autoClose: 5000
            });
            if (inputRef) inputRef.value = '';
            return;
        }
        const tempId = `answerSheet-${Date.now()}`;
        setIsUploading(prev => ({ ...prev, [tempId]: true }));

        const placeholderDoc: TestDocument = {
            id: tempId,
            studentName: studentName,
            rollNumber: rollNumber,
            pdfUrl: file.name,
            className: testDetails.className,
            timestamp: Date.now(),
            uploading: true,
        };
        setTestDocuments(prev => [...prev, placeholderDoc]);

        const currentStudent = studentName;
        const currentRoll = rollNumber;
        setStudentName("");
        setRollNumber("");

        try {
            // const pdfData = await readFileAsDataURL(file);
            setTestDocuments(prevDocs => prevDocs.map(doc =>
                doc.id === tempId
                    ? { ...doc, pdfData: file, uploading: false, error: undefined, studentName: currentStudent, rollNumber: currentRoll }
                    : doc
            ));
            toast.success(`Answer sheet for ${currentStudent} uploaded successfully!`, {
                autoClose: 5000
            });
        } catch (err) {
            console.error(`Error reading answer sheet:`, err);
            const errorMsg = `Failed to read ${file.name}: ${(err as Error).message}`;
            toast.error(errorMsg, {
                autoClose: 5000
            });
            setTestDocuments(prevDocs => prevDocs.map(doc =>
                doc.id === tempId
                    ? { ...doc, uploading: false, error: errorMsg, studentName: currentStudent, rollNumber: currentRoll }
                    : doc
            ));
        } finally {
            setIsUploading(prev => ({ ...prev, [tempId]: false }));
            if (inputRef) inputRef.value = '';
        }
    }, [readFileAsDataURL, studentName, rollNumber, testDetails.className]);

    // const handleSubmitForGrading = useCallback(async () => {
    //     const validAnswerSheets = testDocuments.filter(doc => doc.pdfData && !doc.error);
    //     if (validAnswerSheets.length === 0) {
    //         setError("No valid answer sheets ready for submission.");
    //         return;
    //     }
    //     if (!testDetails.className || !testDetails.subject) {
    //         setError("Please fill in Class Name and Subject.");
    //         return;
    //     }
    //
    //     const questionPaper = rubricDocuments.find(doc => doc.type === 'questionPaper');
    //     const rubric = rubricDocuments.find(doc => doc.type === 'rubric');
    //     
    //     console.log(`[Mehul] [DEBUG] got rubric: ${JSON.stringify(rubric)}, got questionPaper: ${JSON.stringify(questionPaper)}`);
    //
    //     const submissionPayload: TestSubmission = {
    //         testDetails,
    //         answerSheets: validAnswerSheets,
    //         questionPaper,
    //         rubric,
    //         status: GradingStatus.PENDING,
    //         gradingProgress: 0
    //     };
    //
    //     setError("");
    //     const submissionId = `local-${Date.now()}`; // Use a temporary local ID if needed for UI updates
    //     setIsUploading(prev => ({ ...prev, submission: true }));
    //
    //     try {
    //         setTestHistory(prev => [{ ...submissionPayload, id: submissionId }, ...prev]); // Add with local ID
    //
    //         // --- API Call ---
    //         // Simulate API delay for UI feedback
    //         await new Promise(resolve => setTimeout(resolve, 1500));
    //         const response = await axiosPrivate.post('/api/aegisGrader/submit', submissionPayload);
    //
    //         // Update history item status by its local ID
    //         setTestHistory(prev => prev.map(item => item.id === submissionId ? { ...item, status: GradingStatus.IN_PROGRESS } : item));
    //
    //         setTestDocuments([]);
    //         setRubricDocuments([]);
    //         setTestDetails({ className: '', subject: '', date: new Date().toISOString().split('T')[0] });
    //         if (questionPaperInputRef.current) questionPaperInputRef.current.value = ''; // Clear file inputs
    //         if (rubricInputRef.current) rubricInputRef.current.value = '';
    //
    //     } catch (error) {
    //         console.error("Error submitting for grading:", error);
    //         const errorMsg = `Submission failed: ${error instanceof Error ? error.message : String(error)}`;
    //         setError(errorMsg);
    //         setTestHistory(prev => prev.map(item => item.id === submissionId ? { ...item, status: GradingStatus.FAILED } : item));
    //     } finally {
    //         setIsUploading(prev => ({ ...prev, submission: false }));
    //     }
    // }, [testDocuments, rubricDocuments, testDetails]);

    const handleSubmitForGrading = useCallback(async () => {
        const validAnswerSheets = testDocuments.filter(doc => doc.pdfData && !doc.error);
        if (validAnswerSheets.length === 0) {
            toast.error("No valid answer sheets ready for submission.", {
                autoClose: 5000
            });
            return;
        }
        if (!testDetails.subject) {
            toast.error("Please fill in Subject.", {
                autoClose: 5000
            });
            return;
        }

        const questionPaper = rubricDocuments.find(doc => doc.type === 'questionPaper');
        const rubric = rubricDocuments.find(doc => doc.type === 'rubric');

        console.log(`[Mehul] [DEBUG] got rubric: ${JSON.stringify(rubric)}, got questionPaper: ${JSON.stringify(questionPaper)}`);
        console.log(`[Mehul] [DEBUG] got answer sheet names: ${validAnswerSheets.map(doc => doc.pdfUrl)}`);

        let payload: any = {};
        let loadingToast: any = null;

        try {
            if (questionPaper || rubric) {
                // Dismiss any existing submission toasts to prevent duplicates
                toast.dismiss('aegis-grader-submission');

                // Create a single loading toast for the entire submission process
                loadingToast = toast.loading("Preparing submission for grading...", {
                    autoClose: false,
                    toastId: 'aegis-grader-submission' // Unique ID to prevent duplicates
                });

                payload = {
                    questionPaperFile: questionPaper?.pdfUrl,
                    rubricFile: rubric?.pdfUrl,
                    answerSheets: validAnswerSheets.map(doc => {
                        return { filename: doc.pdfUrl, studentName: doc.studentName, rollNumber: doc.rollNumber };
                    }),
                    fileType: "application/pdf",
                    testDetails: {
                        className: "UPSC",
                        subject: testDetails.subject,
                        CreatedBy: user?.id || 'unknown',
                        date: testDetails.date
                    }
                }
            } else {
                throw new Error("Both question paper and rubric must be uploaded before submission.");
            }
            const { data } = await axiosPrivate.post('/api/aegisGrader/getPresigned', payload);
            const { message, uploadUrlQuestion, uploadUrlRubric, uploadUrlSheet, fileNames, manifest, uploadUrlManifest, creditsDeducted, newCreditBalance } = data;
            console.log(`[Mehul] [DEBUG] message: ${message}, Presigned URL response: ${uploadUrlQuestion}, url rubric: ${uploadUrlRubric}, filename: ${fileNames}`);
            console.log(`[Mehul][DEBUG] got sheet urls: ${JSON.stringify(uploadUrlSheet)}`);
            console.log(`[Mehul][DEBUG] Credits deducted: ${creditsDeducted}, New balance: ${newCreditBalance}`);

            // Update credit balance immediately after successful API call
            if (typeof newCreditBalance === 'number') {
                setCreditBalance(newCreditBalance);
            }
            if (questionPaper) {
                await axios.put(uploadUrlQuestion, questionPaper?.pdfData);
            }
            if (rubric) {
                await axios.put(uploadUrlRubric, rubric?.pdfData);
            }

            for (const sheetUrl of uploadUrlSheet) {
                console.log(`[Mehul][DEBUG] Uploading sheet: ${sheetUrl.sheetName} to ${sheetUrl.uploadUrl}`);
                const matchSheet = validAnswerSheets.find(doc => doc.pdfUrl === sheetUrl.sheetName);
                if (matchSheet) {
                    await axios.put(sheetUrl.uploadUrl, matchSheet.pdfData);
                } else {
                    console.error(`Answer sheet with name: ${sheetUrl.sheetName} not found in valid answer sheets`);
                }
            }

            // now write the manifest file
            console.log(`[Mehul][DEBUG] Got manifest presigned url: ${uploadUrlManifest}`);
            const manifestBlob = new Blob([JSON.stringify(manifest, null, 2)], { type: 'application/json' });

            await axios.put(uploadUrlManifest, manifestBlob, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (loadingToast) {
                toast.update(loadingToast, {
                    render: `Successfully submitted ${validAnswerSheets.length} answer sheet${validAnswerSheets.length !== 1 ? 's' : ''} for grading! ${creditsDeducted || validAnswerSheets.length} credit${(creditsDeducted || validAnswerSheets.length) !== 1 ? 's' : ''} deducted. Once graded, results will appear in the grading submissions page.`,
                    type: 'success',
                    isLoading: false,
                    autoClose: 5000
                });
            } else {
                // Fallback in case loadingToast is null
                toast.success(`Successfully submitted ${validAnswerSheets.length} answer sheet${validAnswerSheets.length !== 1 ? 's' : ''} for grading! ${creditsDeducted || validAnswerSheets.length} credit${(creditsDeducted || validAnswerSheets.length) !== 1 ? 's' : ''} deducted. Once graded, results will appear in the grading submissions page.`, {
                    autoClose: 5000
                });
            }
            setTestDocuments([]);
            setRubricDocuments([]);
            setTestDetails({ createdBy: user?.id || '', className: '', subject: '', date: new Date().toISOString().split('T')[0] });
            if (questionPaperInputRef.current) questionPaperInputRef.current.value = ''; // Clear file inputs
            if (rubricInputRef.current) rubricInputRef.current.value = '';

        } catch (error) {
            console.error("Error during submission:", error);
            if (loadingToast) {
                toast.update(loadingToast, {
                    render: `Submission failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    type: 'error',
                    isLoading: false,
                    autoClose: 5000
                });
            } else {
                // Fallback in case loadingToast is null
                toast.error(`Submission failed: ${error instanceof Error ? error.message : 'Unknown error'}`, {
                    autoClose: 5000
                });
            }
        } finally {
            setIsUploading(prev => ({ ...prev, submission: false }));
            // Ensure any remaining loading toasts are dismissed
            if (loadingToast) {
                toast.dismiss(loadingToast);
            }
        }
    }, [testDocuments, rubricDocuments, testDetails]);


    const getRubricDoc = (type: 'questionPaper' | 'rubric') => rubricDocuments.find(doc => doc.type === type);

    // --- Rendering ---
    return (
        <div className="min-h-screen lg:h-screen w-full bg-background flex flex-col lg:overflow-auto lg:pb-0 pb-16">
            <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme={localStorage.getItem('theme') === 'dark' ? 'dark' : 'light'}
            />

            {/* Header - Fixed */}
            <header className="flex-shrink-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border/40 px-3 sm:px-4 py-2 sm:py-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                        <AegisScholarLogoWithoutText
                            className="w-7 h-7 sm:w-8 sm:h-8 lg:hidden flex-shrink-0"
                            style={{ fill: 'var(--color-accent)' }}
                        />
                        <div className="min-w-0">
                            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold font-['Space_Grotesk'] text-foreground truncate">AegisGrader</h1>
                            <p className="text-xs text-muted-foreground truncate">
                                Welcome back, <span className="font-bold">{user?.firstName || user?.username}</span>!
                            </p>
                        </div>
                    </div>
                    <CreditBalance
                        onPurchaseClick={() => setShowCreditPurchaseModal(true)}
                        className="flex-shrink-0"
                        externalBalance={creditBalance}
                    />
                </div>
            </header>

            {/* Main Content - Flexible */}
            <div className="flex-1 min-h-0 p-2 sm:p-3 lg:p-4">
                <div className="h-full grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-2 gap-3 sm:gap-4">

                    {/* Left Column - Test Setup */}
                    <div className="lg:col-span-2 xl:col-span-1 space-y-3 sm:space-y-4 flex flex-col min-h-0 h-full">
                        {/* Test Details with Question Paper & Rubric */}
                        <div className="bg-card rounded-lg shadow-md border border-border/50 p-3 sm:p-4 flex-shrink-0" data-tour="test-details">
                            <h2 className="text-md font-semibold mb-2 sm:mb-3 text-foreground border-b border-border/50 pb-2">1. Test Details</h2>
                            <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4">
                                {/* Left side - Test Details */}
                                <div className="space-y-2 sm:space-y-3">
                                    <div>
                                        <label htmlFor="subject" className="block text-sm text-muted-foreground font-medium mb-1">Subject</label>
                                        <select
                                            id="subject"
                                            value={testDetails.subject}
                                            onChange={e => setTestDetails(prev => ({ ...prev, subject: e.target.value }))}
                                            className="text-sm block w-full border border-border rounded-md p-2 bg-background focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                                        >
                                            <option value="" disabled>Select a subject</option>
                                            {subjects.map(subject => (
                                                <option key={subject} value={subject}>{subject}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <Input
                                        label="Test Date" id="test-date" type="date"
                                        value={testDetails.date}
                                        onChange={(e) => setTestDetails(prev => ({ ...prev, date: e.target.value }))}
                                        className="text-sm"
                                    />
                                </div>

                                {/* Right side - Question Paper & Rubric */}
                                <div className="space-y-2 sm:space-y-3" data-tour="question-paper">
                                    {/* <h3 className="text-sm font-medium text-foreground">Question Paper & Rubric</h3> */}
                                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                                        <div>
                                            <input
                                                type="file" accept="application/pdf"
                                                onChange={handleDocumentUpload('questionPaper')}
                                                className="hidden" id="question-paper-upload"
                                                ref={questionPaperInputRef}
                                                disabled={!!isUploading[getRubricDoc('questionPaper')?.timestamp?.toString() ?? 'questionPaper']}
                                            />
                                            <FileInputLabel
                                                htmlFor="question-paper-upload"
                                                icon={DocumentTextIcon}
                                                text="Question Paper"
                                                uploadedFile={getRubricDoc('questionPaper')?.pdfUrl}
                                                isLoading={isUploading[getRubricDoc('questionPaper')?.timestamp?.toString() ?? 'questionPaper']}
                                            />
                                        </div>
                                        <div data-tour="rubric">
                                            <input
                                                type="file" accept="application/pdf"
                                                onChange={handleDocumentUpload('rubric')}
                                                className="hidden" id="rubric-upload"
                                                ref={rubricInputRef}
                                                disabled={!!isUploading[getRubricDoc('rubric')?.timestamp?.toString() ?? 'rubric']}
                                            />
                                            <FileInputLabel
                                                htmlFor="rubric-upload"
                                                icon={DocumentTextIcon}
                                                text="Rubric"
                                                uploadedFile={getRubricDoc('rubric')?.pdfUrl}
                                                isLoading={isUploading[getRubricDoc('rubric')?.timestamp?.toString() ?? 'rubric']}
                                            />
                                        </div>
                                    </div>

                                    {/* Uploaded Document Previews - Compact */}
                                    {(getRubricDoc('questionPaper') || getRubricDoc('rubric')) && (
                                        <div className="space-y-2 pt-3 border-t border-border/50 mt-3">
                                            <h4 className="text-xs font-medium text-muted-foreground">Uploaded:</h4>
                                            <div className="space-y-1">
                                                {getRubricDoc('questionPaper') && (
                                                    <div className="flex items-center justify-between gap-2 p-2 rounded-md bg-background/50 border border-border/50">
                                                        <div className="flex items-center gap-2 min-w-0 flex-1">
                                                            <DocumentTextIcon className="h-4 w-4 text-primary flex-shrink-0" />
                                                            <span className="text-xs font-medium text-foreground truncate">Question Paper</span>
                                                        </div>
                                                        <button
                                                            onClick={() => {
                                                                const doc = getRubricDoc('questionPaper');
                                                                if (doc?.pdfData instanceof File) {
                                                                    setSelectedDocumentPreview(doc.pdfData);
                                                                }
                                                            }}
                                                            className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 flex items-center gap-1 flex-shrink-0"
                                                            disabled={!getRubricDoc('questionPaper')?.pdfData}
                                                        >
                                                            <EyeIcon className="h-3 w-3" /> Preview
                                                        </button>
                                                    </div>
                                                )}
                                                {getRubricDoc('rubric') && (
                                                    <div className="flex items-center justify-between gap-2 p-2 rounded-md bg-background/50 border border-border/50">
                                                        <div className="flex items-center gap-2 min-w-0 flex-1">
                                                            <DocumentTextIcon className="h-4 w-4 text-primary flex-shrink-0" />
                                                            <span className="text-xs font-medium text-foreground truncate">Rubric</span>
                                                        </div>
                                                        <button
                                                            onClick={() => {
                                                                const doc = getRubricDoc('rubric');
                                                                if (doc?.pdfData instanceof File) {
                                                                    setSelectedDocumentPreview(doc.pdfData);
                                                                }
                                                            }}
                                                            className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 flex items-center gap-1 flex-shrink-0"
                                                            disabled={!getRubricDoc('rubric')?.pdfData}
                                                        >
                                                            <EyeIcon className="h-3 w-3" /> Preview
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Answer Sheet Scanning */}
                        <div className="bg-card rounded-lg shadow-md border border-border/50 p-3 sm:p-4 flex flex-col flex-1 min-h-0 overflow-hidden" data-tour="answer-sheets">
                            <h2 className="text-md font-semibold mb-2 sm:mb-3 text-foreground border-b border-border/50 pb-2">2. Add Answer Sheets</h2>
                            
                            <div className="grid grid-cols-2 gap-2 sm:gap-3 mb-2 sm:mb-3 flex-shrink-0">
                                <Input id="student-name" placeholder="Student name" value={studentName} onChange={(e) => setStudentName(e.target.value)} disabled={isScanning} className="text-sm" />
                                <Input id="roll-number" placeholder="Student roll #" value={rollNumber} onChange={(e) => setRollNumber(e.target.value)} disabled={isScanning} className="text-sm" />
                            </div>

                            {/* Redesigned Layout: Grid on desktop, single column on mobile */}
                            <div className="flex-1 min-h-0 lg:grid lg:grid-cols-2 lg:gap-x-4">
                                
                                {/* Col 1: Camera/Upload Area */}
                                <div className={`lg:h-full flex flex-col min-h-[250px] ${scannedPages.length === 0 ? 'lg:col-span-2' : ''}`}>
                                    {isScanning ? (
                                        <div className="relative w-full flex-1 bg-background rounded-lg overflow-hidden border border-border/50">
                                            <video ref={videoRef} autoPlay playsInline muted className="absolute inset-0 w-full h-full object-contain" style={{ display: cameraLoading ? 'none' : 'block' }} />
                                            {cameraLoading && (
                                                <div className="absolute inset-0 flex flex-col items-center justify-center text-foreground bg-background/70">
                                                    <p className="mb-2 text-sm">Initializing camera...</p>
                                                    <PulsatingDots />
                                                </div>
                                            )}
                                            <div className="absolute bottom-2 sm:bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                                                <button onClick={capturePage} className="px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm bg-info text-info-foreground rounded-md hover:bg-info/90 flex items-center gap-1 sm:gap-2 touch-manipulation" disabled={cameraLoading || !streamRef.current}>
                                                    <CameraIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                                                    <span className="hidden sm:inline">Capture</span>
                                                </button>
                                                <button onClick={stopScanning} className="px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 flex items-center gap-1 sm:gap-2 touch-manipulation">
                                                    <XMarkIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                                                    <span className="hidden sm:inline">Stop</span>
                                                </button>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex flex-col items-center justify-center gap-2 sm:gap-3 flex-1 border-2 border-dashed border-border rounded-lg bg-background/50 text-center p-2 sm:p-4">
                                            <div className="flex flex-row items-center justify-center gap-3 sm:gap-4">
                                                <button onClick={startScanning} disabled={cameraLoading} className="group flex flex-col items-center gap-1 sm:gap-2 p-3 sm:p-4 rounded-lg hover:bg-muted/50 transition-all duration-200 touch-manipulation">
                                                    <div className="p-2 sm:p-3 rounded-full bg-secondary/10 group-hover:bg-secondary/20"><CameraIcon className="h-6 w-6 sm:h-8 sm:w-8 text-primary" /></div>
                                                    <span className="text-xs sm:text-sm font-medium">Scan with Camera</span>
                                                </button>
                                                <div className="h-12 w-px bg-border"></div>
                                                <label htmlFor="answer-sheet-upload" className="group flex flex-col items-center gap-1 sm:gap-2 p-3 sm:p-4 rounded-lg hover:bg-muted/50 transition-all duration-200 cursor-pointer touch-manipulation">
                                                    <div className="p-2 sm:p-3 rounded-full bg-secondary/10 group-hover:bg-secondary/20"><DocumentArrowUpIcon className="h-6 w-6 sm:h-8 sm:w-8 text-secondary-foreground" /></div>
                                                    <span className="text-xs sm:text-sm font-medium">Upload PDF</span>
                                                </label>
                                                <input type="file" accept="application/pdf" onChange={handleAnswerSheetUpload} className="hidden" id="answer-sheet-upload" ref={answerSheetInputRef} disabled={Object.values(isUploading).some(Boolean)} />
                                            </div>
                                            <p className="text-xs text-muted-foreground max-w-md px-2">
                                                {scannedPages.length > 0 ? "Click 'Finish Scanning' below to save the scanned PDF." : "Choose your preferred method to get started"}
                                            </p>
                                        </div>
                                    )}
                                </div>

                                {/* Col 2: Scanned Pages List (Visible on desktop when pages > 0) */}
                                {scannedPages.length > 0 && (
                                    <div className="flex flex-col min-h-0 border-t border-border/50 mt-3 pt-3 lg:border-t-0 lg:mt-0 lg:pt-0">
                                        <div className="flex justify-between items-center mb-2 flex-shrink-0">
                                            <h3 className="text-xs sm:text-sm font-medium text-muted-foreground">Scanned Pages ({scannedPages.length})</h3>
                                            <button onClick={finishScanning} className="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-success text-success-foreground rounded-md hover:bg-success/90 flex items-center gap-1 touch-manipulation" disabled={isUploading['scannedPdf'] || !studentName || !rollNumber}>
                                                {isUploading['scannedPdf'] ? <ArrowPathIcon className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" /> : <CheckCircleSolid className="h-3 w-3 sm:h-4 sm:w-4" />}
                                                {isUploading['scannedPdf'] ? 'Saving...' : 'Finish'}
                                            </button>
                                        </div>
                                        <div className="flex-1 grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-3 gap-1 sm:gap-2 overflow-y-auto scrollbar-thin scrollbar-thumb-border/50 min-h-0">
                                            {scannedPages.map((page, index) => (
                                                <div key={page.timestamp} className="relative group">
                                                    <img src={page.imageUrl} alt={`Page ${index + 1}`} className="w-full aspect-[3/4] object-cover rounded border border-border/50" />
                                                    <button className="absolute top-0.5 right-0.5 p-0.5 sm:p-1 bg-card/80 backdrop-blur-sm rounded-full text-muted-foreground hover:bg-destructive hover:text-destructive-foreground transition-colors border border-border/50 touch-manipulation" onClick={() => setScannedPages(prev => prev.filter(p => p.timestamp !== page.timestamp))}>
                                                        <TrashIcon className="w-2 h-2 sm:w-3 sm:h-3" />
                                                    </button>
                                                    <span className="relative bottom-5 px-1 text-[10px] sm:text-xs bg-black/50 text-white rounded">{index + 1}</span>
                                                </div>
                                            ))}
                                        </div>
                                        {(!studentName || !rollNumber) && <p className="text-xs text-warning mt-1 flex-shrink-0">Enter Student Name & Roll Number to save.</p>}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Right Column - Answer Sheets List */}
                    <div className="lg:col-span-1 xl:col-span-1 bg-card rounded-lg shadow-md border border-border/50 p-3 sm:p-4 flex flex-col min-h-0">
                        <div className="flex justify-between items-center mb-2 sm:mb-3 pb-2 border-b border-border/50 flex-shrink-0">
                            <h2 className="text-md font-semibold text-foreground">3. Ready Sheets ({testDocuments.filter(d => d.pdfData && !d.error).length})</h2>
                        </div>

                        {/* Answer Sheets List - Scrollable */}
                        <div className="flex-1 min-h-0 space-y-1 sm:space-y-2 overflow-y-auto pr-1 sm:pr-2 scrollbar-thin scrollbar-thumb-border/50 max-h-[224px] sm:max-h-none">
                            {testDocuments.length === 0 ? (
                                <p className="text-center text-xs sm:text-sm text-muted-foreground py-6 sm:py-8">No answer sheets added yet.</p>
                            ) : (
                                testDocuments.map((doc) => (
                                    <div key={doc.id} className={`flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border text-xs sm:text-sm ${doc.error ? 'border-destructive/40 bg-destructive/5' : doc.uploading ? 'border-info/40 bg-info/5 animate-pulse' : 'border-border/50 bg-background/50'}`}>
                                        <div className={`flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-[10px] sm:text-xs font-medium ${doc.error ? 'bg-destructive/20 text-destructive' : doc.uploading ? 'bg-info/20 text-info' : 'bg-primary/10 text-primary'}`}>
                                            {doc.studentName?.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase() || '?'}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="font-medium text-foreground truncate">{doc.studentName} <span className="text-muted-foreground">({doc.rollNumber})</span></p>
                                            <p className="text-[10px] sm:text-xs text-muted-foreground truncate" title={doc.pdfUrl}>{doc.pdfUrl}</p>
                                            {doc.uploading && <p className="text-[10px] sm:text-xs text-info">Processing PDF...</p>}
                                            {doc.error && <p className="text-[10px] sm:text-xs text-destructive truncate" title={doc.error}>Error: {doc.error.substring(0, 30)}...</p>}
                                        </div>
                                        <div className="flex gap-0.5 sm:gap-1 flex-shrink-0">
                                            {!doc.uploading && !doc.error && doc.pdfData && (
                                                <button
                                                    onClick={() => {
                                                        if (doc?.pdfData instanceof File) {
                                                            setSelectedDocumentPreview(doc.pdfData);
                                                        }
                                                    }}
                                                    className="p-1 sm:p-1.5 text-muted-foreground hover:text-primary hover:bg-primary/10 rounded-md transition-colors touch-manipulation"
                                                    title="Preview PDF"
                                                >
                                                    <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                                                </button>
                                            )}
                                            <button
                                                onClick={() => handleRemoveAnswerSheet(doc.id)}
                                                className="p-1 sm:p-1.5 text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-md transition-colors touch-manipulation"
                                                title="Remove Sheet"
                                            >
                                                <TrashIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                                            </button>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>

                        {/* Submit Button - Fixed at bottom */}
                        <div className="pt-3 sm:pt-4 border-t border-border/50 mt-3 sm:mt-4 flex-shrink-0">
                            <button
                                onClick={handleButtonPress(handleSubmitForGrading)}
                                disabled={testDocuments.filter(d => d.pdfData && !d.error).length === 0 || Object.values(isUploading).some(Boolean)}
                                className="w-full flex items-center justify-center gap-1 sm:gap-2 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg shadow-md hover:shadow-lg hover:from-primary/90 hover:to-primary/70 transition-all duration-300 disabled:cursor-not-allowed disabled:shadow-none disabled:opacity-60 text-xs sm:text-sm font-medium touch-manipulation"
                                data-tour="submit-grading"
                            >
                                {isUploading['submission'] ? <PulsatingDots /> : <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />}
                                <span className="text-center min-w-0">
                                    {isUploading['submission'] ? 'Submitting...' :
                                        `Submit ${testDocuments.filter(d => d.pdfData && !d.error).length} Sheet${testDocuments.filter(d => d.pdfData && !d.error).length !== 1 ? 's' : ''} (${testDocuments.filter(d => d.pdfData && !d.error).length} credit${testDocuments.filter(d => d.pdfData && !d.error).length !== 1 ? 's' : ''})`
                                    }
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Document Preview Modal */}
            {selectedDocumentPreview && createPortal(
                <div className="fixed inset-0 z-50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4">
                    <div className="bg-card border border-border rounded-xl shadow-2xl h-full w-full max-w-4xl flex flex-col max-h-[75vh] lg:max-h-[90vh] sm:max-h-full">
                        <div className="flex justify-between items-center p-3 sm:p-4 border-b border-border/50 flex-shrink-0">
                            <h3 className="text-base sm:text-lg font-semibold text-foreground">Document Preview</h3>
                            <button
                                onClick={() => setSelectedDocumentPreview(null)}
                                className="p-1.5 sm:p-2 text-muted-foreground hover:bg-muted rounded-full transition-colors touch-manipulation"
                                aria-label="Close preview"
                            >
                                <XMarkIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                            </button>
                        </div>
                        <div className="flex-grow p-1 sm:p-2 overflow-hidden">
                            <DocumentPreview
                                pdfBlob={new Blob([selectedDocumentPreview], { type: 'application/pdf' })}
                            />
                        </div>
                    </div>
                </div>,
                document.body
            )}

            {/* Credit Purchase Modal */}
            <CreditPurchaseModal
                isOpen={showCreditPurchaseModal}
                onClose={() => setShowCreditPurchaseModal(false)}
                onSuccess={(creditsAdded, newBalance) => {
                    setCreditBalance(newBalance);
                    toast.success(`Successfully purchased ${creditsAdded} credits! New balance: ${newBalance}`, {
                        autoClose: 5000
                    });
                }}
            />
        </div>
    );
};

export default AegisGrader;
