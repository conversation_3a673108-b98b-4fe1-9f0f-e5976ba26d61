// components/GradingDetails/index.tsx
import React, { useMemo, useState, useCallback } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import { SubmissionData, AnswerSheetResult, AnswerSheetData, ClassStats } from '../types/gradingTypes';
import { EVALUATION_CONFIG, CONSTANTS } from '../config/evaluationConfig';
import { parseQuestionBreakdown } from '@/components/QuestionBreakdown';
import { Header } from '../components/GradingDetailsComponents/Header';
import { TestInfo } from '../components/GradingDetailsComponents/TestInfo';
import { StudentList } from '../components/GradingDetailsComponents/StudentList';
import { StatsSidebar } from '../components/GradingDetailsComponents/StatsSidebar';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { useUser } from '@/contexts/userContext';
import { fetchWithCache } from '@/utils/cacheUtil';

export const GradingDetails: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { id: submissionId } = useParams<{ id: string }>();
    const [searchTerm, setSearchTerm] = useState('');
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();
    // State for submission data (can be refreshed)
    const [submissionData, setSubmissionData] = useState<SubmissionData | undefined>(() => {
        const history = location.state?.testHistory as SubmissionData[] | undefined;
        return history?.find((sub: SubmissionData) => sub.id === submissionId);
    });

    // Get submission data (use state instead of memo for refreshability)
    const submission = submissionData;

    // Function to refresh submission data
    const refreshSubmissionData = useCallback(async () => {
        if (!submissionId || !user?.id) return;

        try {
            // Fetch updated submissions data
            const response = await fetchWithCache(axiosPrivate, `/api/aegisGrader/submissions/${user.id}`);
            const submissions = response.submissions || [];

            // Find the updated submission
            const updatedSubmission = submissions.find((sub: SubmissionData) => sub.id === submissionId);
            if (updatedSubmission) {
                setSubmissionData(updatedSubmission);
            }
        } catch (error) {
            console.error('Error refreshing submission data:', error);
        }
    }, [submissionId, user?.id, axiosPrivate]);

    // Enhanced result formatter - now accepts sheet object directly
    const formatResults = useMemo(() => (sheet: AnswerSheetData): AnswerSheetResult | null => {
        const parsedEvaluation = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
        if (!parsedEvaluation) return null;

        const detailedBreakdown = parseQuestionBreakdown(sheet.evaluationResult);
        const sections = Array.isArray(parsedEvaluation.section) ? parsedEvaluation.section : [parsedEvaluation.section];

        const questions = sections.flatMap((section) => {
            const questionsInSection = Array.isArray(section.question) ? section.question : [section.question];
            return questionsInSection.map((q) => ({
                questionNumber: q.question_number || 0,
                maxMarks: q.marks_possible || 0,
                marksAwarded: q.marks_awarded || 0,
                feedback: q.feedback || ''
            }));
        }).sort((a, b) => a.questionNumber - b.questionNumber);

        const totalMarks = parsedEvaluation.total_marks || 0;
        const maxMarks = parsedEvaluation.maximum_possible_marks || 0;
        let percentage = parsedEvaluation.percentage_score || 0;

        if (percentage === 0 && maxMarks > 0) {
            percentage = Math.round((totalMarks / maxMarks) * 100);
        }

        return {
            id: sheet.id || submissionId || '',
            studentName: sheet.studentName || 'Unknown Student',
            rollNumber: sheet.rollNumber || 'N/A',
            totalMarks,
            maxMarks,
            percentage,
            results: questions,
            detailedBreakdown,
            pdfUrl: sheet.pdfUrl
        };
    }, [submissionId]);

    // Filtered students
    const filteredSheets = useMemo(() => {
        if (!submission?.answerSheets) return [];
        return submission.answerSheets.filter(sheet =>
            (sheet.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
            (sheet.rollNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase())
        );
    }, [submission?.answerSheets, searchTerm]);

    // Class statistics
    const classStats = useMemo((): ClassStats | null => {
        if (!submission) return null;

        const gradedSheets = submission.answerSheets?.filter(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            return parsed !== null;
        });

        if (!gradedSheets || gradedSheets.length === 0) return null;

        const scores = gradedSheets.map(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            if (!parsed) return 0;

            const totalMarks = parsed.total_marks || 0;
            const maxMarks = parsed.maximum_possible_marks || 0;
            let percentage = parsed.percentage_score || 0;

            if (percentage === 0 && maxMarks > 0) {
                percentage = Math.round((totalMarks / maxMarks) * 100);
            }

            return percentage;
        });

        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const highest = Math.max(...scores);
        const lowest = Math.min(...scores);

        return {
            average,
            highest,
            lowest,
            totalStudents: gradedSheets.length,
            totalSubmissions: submission.answerSheets.length
        };
    }, [submission]);

    // Handle navigation to question breakdown
    const handleViewResults = (result: AnswerSheetResult) => {
        navigate(`/question-breakdown/${result.id}`, {
            state: { submissionData: result }
        });
    };

    if (!submission) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background px-4">
                <div className="text-center">
                    <p className="text-muted-foreground mb-4">Submission not found</p>
                    <button onClick={() => navigate(-1)} className="text-primary hover:underline">
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background p-2 sm:p-4 pb-16">
            <ToastContainer position="top-right" autoClose={CONSTANTS.TOAST_DELAY} />

            <div className="space-y-3 sm:space-y-4 pt-2">
                <Header status={submission.status} onBack={() => navigate(-1)} />
                <TestInfo testDetails={submission.testDetails} />

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4">
                    <StudentList
                        sheets={filteredSheets}
                        searchTerm={searchTerm}
                        onSearchChange={setSearchTerm}
                        onViewResults={handleViewResults}
                        formatResults={formatResults}
                        onRefreshData={refreshSubmissionData}
                        submissionId={submissionId}
                    />
                    <StatsSidebar stats={classStats} />
                </div>
            </div>
        </div>
    );
};

export default GradingDetails;
