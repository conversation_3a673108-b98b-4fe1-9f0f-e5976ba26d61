import React from 'react';
import {
    XMarkIcon,
    DocumentArrowDownIcon,
    CreditCardIcon,
    CalendarIcon,
    IdentificationIcon,
    BanknotesIcon,
    CheckCircleIcon,
    UserIcon,
    EnvelopeIcon,
    CurrencyRupeeIcon,
    DocumentTextIcon
} from '@heroicons/react/24/outline';
import { ReceiptModalProps } from '@/types/billing';
import { useUser } from '@/contexts/userContext';

const ReceiptModal: React.FC<ReceiptModalProps> = ({
    isOpen,
    onClose,
    transaction,
    onDownload
}) => {
    const { user } = useUser();

    if (!isOpen) return null;

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleDownload = () => {
        // Create a printable receipt
        const receiptContent = `
            <html>
                <head>
                    <title>Payment Receipt - ${transaction.transactionId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .logo { font-size: 24px; font-weight: bold; color: #333; }
                        .receipt-title { font-size: 18px; margin-top: 10px; }
                        .section { margin: 20px 0; }
                        .section-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                        .row { display: flex; justify-content: space-between; margin: 8px 0; }
                        .label { font-weight: bold; }
                        .value { text-align: right; }
                        .total { font-size: 18px; font-weight: bold; border-top: 2px solid #333; padding-top: 10px; margin-top: 20px; }
                        .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">AegisScholar</div>
                        <div class="receipt-title">Payment Receipt</div>
                    </div>
                    
                    <div class="section">
                        <div class="section-title">Transaction Details</div>
                        <div class="row">
                            <span class="label">Transaction ID:</span>
                            <span class="value">${transaction.transactionId}</span>
                        </div>
                        <div class="row">
                            <span class="label">Payment ID:</span>
                            <span class="value">${transaction.payment?.razorpayPaymentId || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Order ID:</span>
                            <span class="value">${transaction.payment?.razorpayOrderId || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Date:</span>
                            <span class="value">${formatDate(transaction.createdAt)}</span>
                        </div>
                        <div class="row">
                            <span class="label">Status:</span>
                            <span class="value">${transaction.status}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Customer Information</div>
                        <div class="row">
                            <span class="label">Name:</span>
                            <span class="value">${user?.firstName} ${user?.lastName}</span>
                        </div>
                        <div class="row">
                            <span class="label">Email:</span>
                            <span class="value">${user?.email}</span>
                        </div>
                        <div class="row">
                            <span class="label">User ID:</span>
                            <span class="value">${user?.id}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Purchase Details</div>
                        <div class="row">
                            <span class="label">Package:</span>
                            <span class="value">${transaction.payment?.packageName || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Credits Added:</span>
                            <span class="value">${transaction.creditAmount} credits</span>
                        </div>
                        <div class="row">
                            <span class="label">Amount Paid:</span>
                            <span class="value">${transaction.payment?.amount ? formatAmount(transaction.payment.amount) : 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Currency:</span>
                            <span class="value">${transaction.payment?.currency || 'INR'}</span>
                        </div>
                    </div>

                    <div class="total">
                        <div class="row">
                            <span class="label">Total Amount:</span>
                            <span class="value">${transaction.payment?.amount ? formatAmount(transaction.payment.amount) : 'N/A'}</span>
                        </div>
                    </div>

                    <div class="footer">
                        <p>Thank you for your purchase!</p>
                        <p>For support, contact <NAME_EMAIL></p>
                        <p>Generated on ${new Date().toLocaleDateString('en-IN')}</p>
                    </div>
                </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(receiptContent);
            printWindow.document.close();
            printWindow.print();
        }

        if (onDownload) {
            onDownload();
        }
    };

    return (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-card rounded-xl border border-border shadow-2xl max-w-2xl w-full max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="bg-muted/20 p-6 border-b border-border rounded-t-xl">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary/10 rounded-lg">
                                <DocumentTextIcon className="w-6 h-6 text-primary" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-foreground">Payment Receipt</h2>
                                <p className="text-sm text-muted-foreground">Transaction confirmation</p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-muted/50 rounded-lg transition-colors"
                        >
                            <XMarkIcon className="w-5 h-5 text-muted-foreground hover:text-foreground" />
                        </button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto">
                    <div className="p-6 space-y-6">
                        {/* Transaction Status */}
                        <div className="flex items-center justify-center gap-3 p-4 bg-success/10 rounded-xl border border-success/20">
                            <div className="p-1 bg-success/10 rounded-full">
                                <CheckCircleIcon className="w-5 h-5 text-success" />
                            </div>
                            <span className="text-success font-semibold">
                                Payment Successful
                            </span>
                        </div>

                        {/* Transaction Details */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <IdentificationIcon className="w-5 h-5 text-primary" />
                                Transaction Details
                            </h3>
                            <div className="bg-muted/30 rounded-xl p-4 space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium text-muted-foreground">Transaction ID</span>
                                        </div>
                                        <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                            {transaction.transactionId}
                                        </p>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium text-muted-foreground">Date & Time</span>
                                        </div>
                                        <p className="text-sm bg-card border border-border p-3 rounded-lg">
                                            {formatDate(transaction.createdAt)}
                                        </p>
                                    </div>
                                </div>

                                {transaction.payment && (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm font-medium text-muted-foreground">Payment ID</span>
                                            </div>
                                            <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                                {transaction.payment.razorpayPaymentId}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm font-medium text-muted-foreground">Order ID</span>
                                            </div>
                                            <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                                {transaction.payment.razorpayOrderId}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Purchase Details */}
                        {transaction.payment && (
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                    <img src="/money_filled.png" alt="Credits" className="w-5 h-5" />
                                    Purchase Details
                                </h3>
                                <div className="bg-primary/5 rounded-xl p-5 border border-primary/20">
                                    <div className="space-y-4">
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Package:</span>
                                            <span className="font-semibold text-foreground">{transaction.payment.packageName}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Credits Added:</span>
                                            <div className="flex items-center gap-2">
                                                <span className="font-semibold text-success text-lg">
                                                    +{transaction.creditAmount}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Amount Paid:</span>
                                            <span className="font-semibold text-foreground">{formatAmount(transaction.payment.amount)}</span>
                                        </div>
                                        <div className="border-t border-primary/20 pt-4">
                                            <div className="flex justify-between items-center">
                                                <span className="font-bold text-foreground">Total:</span>
                                                <span className="font-bold text-xl text-primary">
                                                    {formatAmount(transaction.payment.amount)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Customer Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <UserIcon className="w-5 h-5 text-primary" />
                                Customer Information
                            </h3>
                            <div className="bg-muted/30 rounded-xl p-4 space-y-3">
                                <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-2">
                                        <UserIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-muted-foreground font-medium">Name:</span>
                                    </div>
                                    <span className="font-semibold">{user?.firstName} {user?.lastName}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-2">
                                        <EnvelopeIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-muted-foreground font-medium">Email:</span>
                                    </div>
                                    <span className="font-semibold">{user?.email}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="bg-muted/20 border-t border-border p-6 rounded-b-xl">
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div className="text-center sm:text-left">
                            <p className="text-sm font-medium text-foreground">
                                Thank you for your purchase! 🎉
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                                For support, contact <NAME_EMAIL>
                            </p>
                        </div>
                        <button
                            onClick={handleDownload}
                            className="flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl"
                        >
                            <DocumentArrowDownIcon className="w-4 h-4" />
                            Download Receipt
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReceiptModal;
