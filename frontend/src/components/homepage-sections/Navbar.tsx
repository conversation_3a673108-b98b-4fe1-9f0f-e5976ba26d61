import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence, useTransform, useMotionValue } from 'framer-motion';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AegisScholarLogoWithoutText from '../../assets/AegisScholarLogoIcon';
import ThemeToggle from '../ThemeToggle';
import { BaseComponentProps, ModalHandlers, ScrollState, MenuState } from './types';

interface NavbarProps extends BaseComponentProps, ModalHandlers, ScrollState, MenuState {
  scrollY: number;
}

const Navbar: React.FC<NavbarProps> = ({
  setRegisterModalOpen,
  setLoginModalOpen,
  isScrolled,
  menuOpen,
  setMenuOpen,
  scrollY,
  className = ''
}) => {
  const navigate = useNavigate();

  // Create motion values for smooth interpolation
  const motionScrollY = useMotionValue(scrollY);

  // Update motion value when scrollY changes
  useEffect(() => {
    motionScrollY.set(scrollY);
  }, [scrollY, motionScrollY]);

  // Smooth transforms based on scroll position
  const navbarScale = useTransform(motionScrollY, [0, 100], [1, 0.95]);
  const navbarOpacity = useTransform(motionScrollY, [0, 50], [0.9, 0.85]);
  const logoScale = useTransform(motionScrollY, [0, 100], [1, 0.8]);
  const paddingY = useTransform(motionScrollY, [0, 100], [16, 12]);
  const borderRadius = useTransform(motionScrollY, [0, 100], [0, 16]);
  const marginTop = useTransform(motionScrollY, [0, 100], [0, 12]);

  // Enhanced easing configuration
  const smoothTransition = {
    type: "spring",
    stiffness: 400,
    damping: 40,
    mass: 0.8
  };

  const ultraSmoothTransition = {
    type: "spring",
    stiffness: 300,
    damping: 35,
    mass: 0.6
  };

  return (
    <>
      {/* Main Navbar with scaling effects */}
      <motion.nav
        className={`fixed top-0 left-0 right-0 z-50 ${className}`}
        style={{ scale: navbarScale }}
        transition={smoothTransition}
      >
        <motion.div
          className={`mx-auto transition-all duration-700 ease-out ${
            isScrolled
              ? 'bg-background/90 border border-border/50 shadow-lg shadow-black/5 max-w-6xl mx-6'
              : 'bg-transparent w-full'
          }`}
          style={{
            opacity: navbarOpacity,
            borderRadius: isScrolled ? borderRadius : 0,
            marginTop: isScrolled ? marginTop : 0
          }}
          transition={ultraSmoothTransition}
        >
          <motion.div
            className="px-6 flex justify-between items-center"
            style={{ paddingTop: paddingY, paddingBottom: paddingY }}
            transition={smoothTransition}
          >
            {/* Logo with enhanced smooth scaling */}
            <motion.div
              className="flex items-center space-x-3"
              style={{ scale: logoScale }}
              transition={ultraSmoothTransition}
            >
              <motion.div
                whileHover={{ scale: 1.05, rotate: 1 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 500, damping: 25 }}
              >
                <AegisScholarLogoWithoutText
                  className={`transition-all duration-700 ease-out ${
                    isScrolled ? 'w-8 h-8' : 'w-10 h-10'
                  }`}
                  style={{ fill: 'var(--color-accent)' }}
                />
              </motion.div>
              <motion.a
                href="/"
                className={`font-bold font-['Space_Grotesk'] text-primary transition-all duration-700 ease-out ${
                  isScrolled ? 'text-lg' : 'text-xl'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                AegisScholar
              </motion.a>
            </motion.div>

            {/* Desktop Navigation with enhanced hover effects */}
            <div className="hidden md:flex items-center space-x-6">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <ThemeToggle size="default" />
              </motion.div>

              <motion.button
                className={`bg-accent text-accent-foreground rounded-xl font-medium transition-all duration-700 ease-out cursor-pointer hover:shadow-lg hover:shadow-accent/25 ${
                  isScrolled ? 'px-4 py-2 text-sm' : 'px-6 py-3'
                }`}
                onClick={() => navigate('/login?type=institution')}
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  boxShadow: "0 10px 25px rgba(0,0,0,0.15)"
                }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                Login
              </motion.button>
            </div>

            {/* Mobile Menu Button with enhanced animation */}
            <div className="flex items-center space-x-3 md:hidden">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <ThemeToggle size="sm" />
              </motion.div>
              <motion.button
                onClick={() => setMenuOpen(!menuOpen)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                className="p-2 rounded-lg hover:bg-muted/50 transition-colors duration-200"
              >
                <motion.div
                  animate={{ rotate: menuOpen ? 180 : 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  {!menuOpen ? <Menu className="w-6 h-6" /> : <X className="w-6 h-6" />}
                </motion.div>
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </motion.nav>

      {/* Mobile Menu - Separate from navbar to avoid scaling issues */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-[60] md:hidden"
          >
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 bg-background/95 backdrop-blur-md"
              onClick={() => setMenuOpen(false)}
            />

            {/* Menu Content */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{
                type: 'spring',
                damping: 25,
                stiffness: 200,
                duration: 0.5
              }}
              className="absolute right-0 top-0 h-full w-full bg-card/95 backdrop-blur-xl border-l border-border flex flex-col"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-border">
                <div className="flex items-center space-x-3">
                  <AegisScholarLogoWithoutText
                    className="w-8 h-8"
                    style={{ fill: 'var(--color-accent)' }}
                  />
                  <span className="font-bold font-['Space_Grotesk'] text-lg text-primary">
                    AegisScholar
                  </span>
                </div>
                <button
                  onClick={() => setMenuOpen(false)}
                  className="p-2 rounded-xl hover:bg-muted/50 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Content - Centered */}
              <div className="flex-1 flex flex-col justify-center px-6">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-foreground mb-2">AegisGrader</h3>
                  <p className="text-foreground/70">AI-powered grading for institutions</p>
                </div>
              </div>

              {/* Bottom Actions - Fixed at Bottom */}
              <div className="p-6 space-y-4 border-t border-border bg-card/50">
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1, duration: 0.3 }}
                  className="w-full bg-accent text-accent-foreground px-6 py-4 rounded-xl font-semibold text-lg transition-all hover:scale-105"
                  onClick={() => {
                    setMenuOpen(false);
                    navigate('/login?type=institution');
                  }}
                >
                  Login
                </motion.button>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.3 }}
                  className="w-full border border-accent text-accent px-6 py-4 rounded-xl font-semibold text-lg transition-all hover:bg-accent/10"
                  onClick={() => {
                    setMenuOpen(false);
                    navigate('/register?type=institution');
                  }}
                >
                  Get Started
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navbar;