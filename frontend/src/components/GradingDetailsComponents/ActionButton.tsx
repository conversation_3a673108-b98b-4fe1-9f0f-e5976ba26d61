// components/StudentCard/ActionButton.tsx
import React from 'react';

interface ActionButtonProps {
    onClick: () => void;
    label: string;
    shortLabel: string;
    isLoading?: boolean;
    disabled?: boolean;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
    onClick,
    label,
    shortLabel,
    isLoading = false,
    disabled = false
}) => (
    <button
        className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-xs sm:text-sm whitespace-nowrap flex-shrink-0 min-h-[36px] sm:min-h-[40px] flex items-center justify-center gap-2"
        onClick={onClick}
        disabled={disabled || isLoading}
    >
        {isLoading && (
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary-foreground"></div>
        )}
        <span className="hidden sm:inline">{isLoading ? 'Loading...' : label}</span>
        <span className="sm:hidden">{isLoading ? 'Loading...' : shortLabel}</span>
    </button>
);