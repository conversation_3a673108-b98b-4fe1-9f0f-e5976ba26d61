import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { Status, StatusUtils, TestSubmission } from '../types/aegisGrader';
import { useNavigate } from 'react-router-dom';
import { useAxiosPrivate } from "@/hooks/useAxiosPrivate";
import { fetchWithCache } from '@/utils/cacheUtil';
import { useMobileInteractions } from '@/hooks/useMobileInteractions';
import { toast } from 'react-toastify';
import { AlertCircle, Search, Filter, X, Calendar, BookOpen, CheckCircle } from 'lucide-react';
import { useUser } from '@/contexts/userContext';
import PulsatingDots from './PulsatingDotsLoader';

// --- Simplified Status Badge ---
const StatusBadge: React.FC<{ status?: Status | string }> = React.memo(({ status }) => {
    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${StatusUtils.getStatusClasses(status)}`}>
            {StatusUtils.getDisplayText(status)}
        </span>
    );
});

export const GradingSubmissions: React.FC = () => {
    const navigate = useNavigate();
    const [testHistory, setTestHistory] = useState<TestSubmission[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [selectedSubject, setSelectedSubject] = useState<string>('');
    const [selectedDateRange, setSelectedDateRange] = useState<string>('');
    const [selectedStatus, setSelectedStatus] = useState<string>('');
    const [showFilters, setShowFilters] = useState<boolean>(false);

    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();

    // Mobile interactions
    const { handleButtonPress } = useMobileInteractions({
        enableSwipe: true,
        swipeThreshold: 60
    });

    useEffect(() => {
        const fetchGradingSubmissions = async () => {
            try {
                setIsLoading(true);
                const data = await fetchWithCache(axiosPrivate, `/api/aegisGrader/submissions/${user?.id}`);
                console.log("Fetched submissions:", data);
                setTestHistory(data.submissions || []);
            } catch (error) {
                console.error("Error fetching submissions:", error);
            } finally {
                setIsLoading(false);
            }
        }
        fetchGradingSubmissions();
    }, [user]);

    // Get unique subjects for filter dropdown
    const uniqueSubjects = useMemo(() => {
        const subjects = testHistory.map(submission => submission.testDetails.subject);
        return Array.from(new Set(subjects)).sort();
    }, [testHistory]);

    // Get unique statuses for filter dropdown
    const uniqueStatuses = useMemo(() => {
        const statuses = testHistory.map(submission => StatusUtils.normalize(submission.status));
        return Array.from(new Set(statuses)).sort();
    }, [testHistory]);

    // Filter submissions based on search and filters
    const filteredSubmissions = useMemo(() => {
        let filtered = testHistory;

        // Search filter
        if (searchTerm.trim()) {
            const searchLower = searchTerm.toLowerCase();
            filtered = filtered.filter(submission =>
                submission.testDetails.subject.toLowerCase().includes(searchLower) ||
                submission.testDetails.className.toLowerCase().includes(searchLower)
            );
        }

        // Subject filter
        if (selectedSubject) {
            filtered = filtered.filter(submission =>
                submission.testDetails.subject === selectedSubject
            );
        }

        // Status filter
        if (selectedStatus) {
            filtered = filtered.filter(submission =>
                StatusUtils.normalize(submission.status) === selectedStatus
            );
        }

        // Date filter
        if (selectedDateRange) {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            filtered = filtered.filter(submission => {
                const submissionDate = new Date(submission.testDetails.date);
                const submissionDateOnly = new Date(submissionDate.getFullYear(), submissionDate.getMonth(), submissionDate.getDate());

                switch (selectedDateRange) {
                    case 'today':
                        return submissionDateOnly.getTime() === today.getTime();
                    case 'week':
                        const weekAgo = new Date(today);
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return submissionDateOnly >= weekAgo;
                    case 'month':
                        const monthAgo = new Date(today);
                        monthAgo.setMonth(monthAgo.getMonth() - 1);
                        return submissionDateOnly >= monthAgo;
                    case 'year':
                        const yearAgo = new Date(today);
                        yearAgo.setFullYear(yearAgo.getFullYear() - 1);
                        return submissionDateOnly >= yearAgo;
                    default:
                        return true;
                }
            });
        }

        return filtered;
    }, [testHistory, searchTerm, selectedSubject, selectedDateRange, selectedStatus]);

    const clearAllFilters = () => {
        setSearchTerm('');
        setSelectedSubject('');
        setSelectedDateRange('');
        setSelectedStatus('');
    };

    const hasActiveFilters = searchTerm || selectedSubject || selectedDateRange || selectedStatus;

    const viewGradingDetails = useCallback((id: string | undefined) => {
        if (!id) {
            toast.error("Invalid submission ID");
            return;
        }

        const submission = testHistory.find(sub => sub.id === id);
        if (!submission) {
            toast.error("Submission not found");
            return;
        }

        navigate(`/gradingDetails/` + id, {
            state: { testHistory },
        });
    }, [navigate, testHistory]);

    return (
        <div className="bg-card rounded-xl shadow-sm border border-border h-full flex flex-col">
            {/* Search and Filter Header */}
            <div className="flex-shrink-0 px-2 py-3 border-b border-border">
                {/* Search Bar and Filter Button Row */}
                <div className="flex items-center justify-between w-full gap-3">
                    {/* Search Bar */}
                    <div className="relative w-fit">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <input
                            type="text"
                            placeholder="Search"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full pl-10 pr-4 py-2.5 bg-background border border-border rounded-lg text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                    </div>

                    {/* Filter Button with Dropdown */}
                    <div className="relative">
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className={`flex items-center gap-2 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${showFilters || hasActiveFilters
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted text-muted-foreground hover:bg-muted/80'
                                }`}
                        >
                            <Filter className="w-4 h-4" />
                            <span className="hidden sm:inline">Filters</span>
                            {hasActiveFilters && (
                                <span className="bg-background/20 text-xs px-1.5 py-0.5 rounded-full">
                                    {[searchTerm, selectedSubject, selectedDateRange, selectedStatus].filter(Boolean).length}
                                </span>
                            )}
                        </button>

                        {/* Filter Options Dropdown */}
                        {showFilters && (
                            <div className="absolute top-full right-0 mt-2 w-80 sm:w-96 bg-card border border-border rounded-lg shadow-lg z-50">
                                <div className="p-4 space-y-4">
                                    {/* Clear All Filters - Inside dropdown */}
                                    {hasActiveFilters && (
                                        <div className="flex justify-end">
                                            <button
                                                onClick={clearAllFilters}
                                                className="flex items-center gap-1 px-2 py-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
                                            >
                                                <X className="w-3 h-3" />
                                                Clear all
                                            </button>
                                        </div>
                                    )}

                                    <div className="grid grid-cols-1 gap-4">
                                        {/* Subject Filter */}
                                        <div>
                                            <label className="flex items-center gap-2 text-xs font-medium text-foreground mb-2">
                                                <BookOpen className="w-3 h-3" />
                                                Subject
                                            </label>
                                            <select
                                                value={selectedSubject}
                                                onChange={(e) => setSelectedSubject(e.target.value)}
                                                className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            >
                                                <option value="">All subjects</option>
                                                {uniqueSubjects.map(subject => (
                                                    <option key={subject} value={subject}>
                                                        {subject}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        {/* Status Filter */}
                                        <div>
                                            <label className="flex items-center gap-2 text-xs font-medium text-foreground mb-2">
                                                <CheckCircle className="w-3 h-3" />
                                                Status
                                            </label>
                                            <select
                                                value={selectedStatus}
                                                onChange={(e) => setSelectedStatus(e.target.value)}
                                                className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            >
                                                <option value="">All statuses</option>
                                                {uniqueStatuses.map(status => (
                                                    <option key={status} value={status}>
                                                        {StatusUtils.getDisplayText(status)}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        {/* Date Filter */}
                                        <div>
                                            <label className="flex items-center gap-2 text-xs font-medium text-foreground mb-2">
                                                <Calendar className="w-3 h-3" />
                                                Date Range
                                            </label>
                                            <select
                                                value={selectedDateRange}
                                                onChange={(e) => setSelectedDateRange(e.target.value)}
                                                className="w-full px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            >
                                                <option value="">All time</option>
                                                <option value="today">Today</option>
                                                <option value="week">Last 7 days</option>
                                                <option value="month">Last month</option>
                                                <option value="year">Last year</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>



            {/* Results Summary */}
            {(hasActiveFilters && !isLoading) && (
                <div className="flex-shrink-0 px-4 py-2 bg-muted/30 border-b border-border">
                    <p className="text-xs text-muted-foreground">
                        Showing {filteredSubmissions.length} of {testHistory.length} submissions
                    </p>
                </div>
            )}

            {/* Content Container - Scrollable */}
            <div className="flex-1 overflow-y-auto p-2">
                <div className="flex flex-col gap-2">
                    {isLoading ? (
                        <div className="flex items-center justify-center h-full min-h-[200px]">
                            <div className="flex flex-col items-center justify-center py-12">
                                <PulsatingDots />
                                <div className="mt-4 align-center text-center">
                                    <h3 className="text-base sm:text-lg font-medium text-foreground mb-1">Loading submissions</h3>
                                    <p className="text-sm text-muted-foreground">Please wait while we fetch your grading submissions</p>
                                </div>
                            </div>
                        </div>
                    ) :
                        filteredSubmissions.length === 0 ? (
                            <div className="text-center text-muted-foreground py-8 sm:py-12">
                                <div className="flex flex-col items-center gap-3">
                                    <div className="w-16 h-16 sm:w-20 sm:h-20 bg-muted rounded-full flex items-center justify-center">
                                        <AlertCircle className="w-8 h-8 sm:w-10 sm:h-10 text-muted-foreground" />
                                    </div>
                                    <div>
                                        <h3 className="text-base sm:text-lg font-medium text-foreground mb-1">
                                            {hasActiveFilters ? 'No matching submissions' : 'No submissions yet'}
                                        </h3>
                                        <p className="text-sm text-muted-foreground">
                                            {hasActiveFilters
                                                ? 'Try adjusting your search or filter criteria'
                                                : 'Your test grading submissions will appear here'
                                            }
                                        </p>
                                        {hasActiveFilters && (
                                            <button
                                                onClick={clearAllFilters}
                                                className="mt-2 text-sm text-primary hover:text-primary/80 transition-colors"
                                            >
                                                Clear filters
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ) : (
                            filteredSubmissions.map((submission, index) => (
                                <div
                                    key={index}
                                    className="border rounded-xl p-4 sm:p-6 hover:bg-muted/50 cursor-pointer transition-all duration-200 hover:shadow-md touch-manipulation mobile-button"
                                    onClick={handleButtonPress(() => viewGradingDetails(submission.id || ''))}
                                >
                                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                                        <div className="flex-1 min-w-0">
                                            {/* Subject and Badge Row - Modified for mobile */}
                                            <div className="flex items-center justify-between gap-2 mb-2">
                                                <h3 className="text-base sm:text-lg font-semibold text-foreground truncate flex-1">
                                                    {submission.testDetails.subject}
                                                </h3>
                                                {/* Badge positioned on the right for both mobile and desktop */}
                                                <StatusBadge status={submission.status || undefined} />
                                            </div>
                                            <p className="text-sm sm:text-base text-muted-foreground">
                                                {submission.testDetails.className} • {new Date(submission.testDetails.date).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2 text-sm text-muted-foreground sm:mt-0">
                                            <span className="bg-muted px-2 py-1 rounded-md">
                                                {submission.answerSheets.length} sheet{submission.answerSheets.length !== 1 ? 's' : ''}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                </div>
            </div>
        </div>
    );
}

export default GradingSubmissions;
