import React, { useState, useRef, useEffect } from 'react';
import {
  ChevronDownIcon,
  UserIcon,
  LogOutIcon,
  SettingsIcon,
  LayoutDashboardIcon,
  BookOpenIcon,
  BookmarkIcon,
  UserPenIcon,
  Moon,
  Sun,
} from "lucide-react"
import { useUser } from '../contexts/userContext';
import { useTheme } from '../contexts/themeContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

// Hook to detect mobile
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
};

interface ProfileDropdownProps {
  className?: string;
  inBottomNav?: boolean; // New prop to indicate if it's in bottom navigation
}

export const ProfileDropdown: React.FC<ProfileDropdownProps> = ({ 
  className = "",
  inBottomNav = false 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, setUser } = useUser();
  const { theme, setTheme } = useTheme();
  const axiosPrivate = useAxiosPrivate();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const getCurrentIcon = () => {
    return theme === 'dark' ? Moon : Sun;
  };

  const handleThemeToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const handleLogout = async () => {
    const logoutToast = toast.loading('Logging out...', { position: "top-right", toastId: 'logout' });
    try {
      let payload: any = {};
      if (user?.role === 'Student') {
        payload = { admissionNumber: user?.admissionNumber, email: user?.email };
      } else {
        payload = { email: user?.email };
      }

      await axiosPrivate.put(`/api/logout/${user?.role}`, payload);
      toast.update(logoutToast, {
        render: 'Logged out successfully!',
        type: 'success',
        isLoading: false,
        autoClose: 1500
      });
    } catch (error) {
      toast.update(logoutToast, {
        render: 'Failed to logout!',
        type: 'error',
        isLoading: false,
        autoClose: 2000
      });
    } finally {
      setUser(null);
      sessionStorage.clear();
      setIsOpen(false);
      setTimeout(() => navigate('/'), 1500);
    }
  };

  const displayName = user?.firstName && user?.lastName 
    ? `${user.firstName} ${user.lastName}` 
    : user?.username || 'User';
  
  const avatarSrc = user?.profileImage?.imageData?.startsWith('data:') 
    ? user.profileImage.imageData 
    : null;

  // Generate initials for fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Close dropdown when pressing Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  const menuItems = [
    // { icon: SettingsIcon, label: 'Settings', onClick: () => console.log('Settings') },
    { icon: getCurrentIcon(), label: 'Theme', onClick: () => handleThemeToggle() },
  ];

  // Mobile-specific rendering
  if (isMobile) {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        {/* Mobile Trigger Button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            flex items-center justify-center transition-all duration-200 
            focus:outline-none focus:ring-1 focus:ring-primary/20 rounded-lg
            ${inBottomNav ? 'w-10 h-10' : 'w-8 h-8'}
          `}
          aria-expanded={isOpen}
          aria-haspopup="true"
        >
          {/* Mobile Avatar */}
          <div className={`rounded-full bg-accent/20 flex items-center justify-center overflow-hidden flex-shrink-0 ${inBottomNav ? 'w-6 h-6' : 'w-8 h-8'}`}>
            {avatarSrc ? (
              <img 
                src={avatarSrc} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-accent/20 text-accent font-medium flex items-center justify-center">
                {user ? (
                  <span className={inBottomNav ? 'text-xs' : 'text-sm'}>
                    {getInitials(displayName)}
                  </span>
                ) : (
                  <UserIcon className={`${inBottomNav ? 'w-3 h-3' : 'w-4 h-4'}`} />
                )}
              </div>
            )}
          </div>
        </button>

        {/* Mobile Dropdown Menu */}
        {isOpen && (
          <>
            {/* Mobile Backdrop */}
            <div 
              className="fixed inset-0 bg-black/20 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Mobile Menu */}
            <div className={`
              fixed z-50 bg-card border border-border rounded-lg
              animate-in slide-in-from-bottom-full duration-300
              ${inBottomNav 
                ? 'bottom-16 left-4 right-4' // Above bottom nav with margins
                : 'bottom-4 left-4 right-4'   // Standard mobile positioning
              }
            `}>
              {/* User Info Header */}
              <div className="px-4 py-4 border-b border-border">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-accent/20 flex items-center justify-center overflow-hidden flex-shrink-0">
                    {avatarSrc ? (
                      <img 
                        src={avatarSrc} 
                        alt="Profile" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-accent/20 text-accent text-sm font-medium flex items-center justify-center">
                        {user ? getInitials(displayName) : <UserIcon className="w-5 h-5" />}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-base font-medium text-foreground truncate">
                      {displayName}
                    </div>
                    <div className="text-sm text-muted-foreground truncate">
                      {user?.email || 'No email'}
                    </div>
                    {user?.role && (
                      <div className="text-xs text-muted-foreground truncate">
                        {user.role}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Menu Items */}
              <div className="">
                {menuItems.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={index}
                      onClick={() => {
                        item.onClick();
                        setIsOpen(false);
                      }}
                      className="w-full flex items-center px-4 py-3 text-base text-foreground hover:bg-accent/10 active:bg-accent/20 transition-colors duration-150"
                    >
                      <Icon className="w-5 h-5 mr-3 opacity-60" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </div>

              {/* Separator */}
              <div className="border-t border-border"></div>

              {/* Logout Button */}
              <div className="">
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-4 py-3 text-base text-foreground hover:bg-accent/10 active:bg-accent/20 transition-colors duration-150"
                >
                  <LogOutIcon className="w-5 h-5 mr-3 opacity-60" />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  // Desktop rendering (original code)
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Desktop Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-10 h-10 flex items-center justify-center rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* Desktop Avatar */}
        <div className="w-6 h-6 rounded-full bg-accent/20 flex items-center justify-center overflow-hidden flex-shrink-0">
          {avatarSrc ? (
            <img 
              src={avatarSrc} 
              alt="Profile" 
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-accent/20 text-accent text-xs font-medium flex items-center justify-center">
              {user ? getInitials(displayName) : <UserIcon className="w-3 h-3" />}
            </div>
          )}
        </div>
      </button>

      {/* Desktop Dropdown Menu */}
      {isOpen && (
        <div className="absolute lg:left-0 md:left-0 right-0 bottom-full mb-2 w-64 bg-card border border-border rounded-lg shadow-lg overflow-hidden z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-border">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center overflow-hidden flex-shrink-0">
                {avatarSrc ? (
                  <img 
                    src={avatarSrc} 
                    alt="Profile" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-accent/20 text-accent text-sm font-medium flex items-center justify-center">
                    {user ? getInitials(displayName) : <UserIcon className="w-4 h-4" />}
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-foreground truncate">
                  {displayName}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {user?.email || 'No email'}
                </div>
                {user?.role && (
                  <div className="text-xs text-muted-foreground truncate">
                    {user.role}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Menu Items */}
          {/* <div className="py-1">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <button
                  key={index}
                  onClick={() => {
                    item.onClick();
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center px-4 py-2 text-sm text-foreground hover:bg-accent/10 transition-colors duration-150 focus:outline-none focus:bg-accent/10"
                >
                  <Icon className="w-4 h-4 mr-3 opacity-60" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </div> */}

          {/* Separator */}
          <div className="border-t border-border"></div>

          {/* Logout Button */}
          <div className="py-1">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 text-sm text-foreground hover:bg-accent/10 transition-colors duration-150 focus:outline-none focus:bg-accent/10"
            >
              <LogOutIcon className="w-4 h-4 mr-3 opacity-60" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
