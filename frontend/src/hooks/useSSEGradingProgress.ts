import { useState, useEffect, useRef } from 'react';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Status, StatusUtils } from '../types/aegisGrader';
import { useUser } from '@/contexts/userContext';

const BASE_URL = process.env.NODE_ENV === 'production' 
    ? 'https://api.aegisscholar.com' 
    : 'http://localhost:8080';

class RetriableError extends Error {}
class FatalError extends Error {}

interface UseSSEGradingProgressProps {
    sheetId: string;
    hasEvaluationResult: boolean;
    initialStatus?: string;
}

interface SSEProgressState {
    progress: number;
    status: string;
    isConnected: boolean;
    error: string | null;
}

export const useSSEGradingProgress = ({
    sheetId,
    hasEvaluationResult,
    initialStatus
}: UseSSEGradingProgressProps): SSEProgressState => {
    const { user } = useUser();
    const abortControllerRef = useRef<AbortController | null>(null);
    const isMountedRef = useRef(true);

    const [state, setState] = useState<SSEProgressState>(() => ({
        progress: hasEvaluationResult ? 100 : 0,
        status: hasEvaluationResult ? Status.COMPLETED : (initialStatus || Status.PENDING),
        isConnected: false,
        error: null
    }));

    useEffect(() => {
        isMountedRef.current = true;
        
        // Don't start SSE if already completed or missing requirements
        if (hasEvaluationResult || !sheetId || !user?.accessToken) {
            return;
        }

        const normalizedStatus = StatusUtils.normalize(initialStatus);
        if (normalizedStatus === Status.COMPLETED || normalizedStatus === Status.ERROR) {
            return;
        }

        const startSSE = async () => {
            abortControllerRef.current = new AbortController();
            
            try {
                setState(prev => ({ ...prev, isConnected: true, error: null }));
                
                await fetchEventSource(`${BASE_URL}/api/aegisGrader/getCurrentProgress/${sheetId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${user.accessToken}`,
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    },
                    signal: abortControllerRef.current.signal,

                    async onopen(response) {
                        if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
                            return;
                        } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
                            throw new FatalError(`Client error: ${response.status}`);
                        } else {
                            throw new RetriableError(`Server error: ${response.status}`);
                        }
                    },

                    onmessage(event) {
                        if (!isMountedRef.current) return;

                        try {
                            const data = JSON.parse(event.data);
                            
                            setState(prev => ({
                                ...prev,
                                progress: data.progress || 0,
                                status: data.status || Status.PENDING
                            }));

                            const normalizedDataStatus = StatusUtils.normalize(data.status);
                            
                            if (data.progress === 100 && normalizedDataStatus === Status.PROCESSING) {
                                setState(prev => ({ ...prev, status: Status.EVALUATING }));
                            }

                            if (normalizedDataStatus === Status.COMPLETED) {
                                setState(prev => ({ 
                                    ...prev, 
                                    progress: 100, 
                                    status: Status.COMPLETED,
                                    isConnected: false 
                                }));
                                abortControllerRef.current?.abort();
                                return;
                            }

                            if (normalizedDataStatus === Status.ERROR) {
                                throw new FatalError('Server reported error status');
                            }
                        } catch (parseError) {
                            if (parseError instanceof FatalError) {
                                throw parseError;
                            }
                        }
                    },

                    onclose() {
                        setState(prev => ({ ...prev, isConnected: false }));
                        const currentNormalizedStatus = StatusUtils.normalize(state.status);
                        if (isMountedRef.current && 
                            currentNormalizedStatus !== Status.COMPLETED && 
                            currentNormalizedStatus !== Status.ERROR) {
                            throw new RetriableError('Connection closed unexpectedly');
                        }
                    },

                    onerror(err) {
                        if (!isMountedRef.current) return;

                        setState(prev => ({ ...prev, isConnected: false }));

                        if (err instanceof FatalError) {
                            setState(prev => ({ ...prev, status: Status.ERROR, error: err.message }));
                            throw err;
                        } else if (err.name === 'AbortError') {
                            return;
                        }
                    },

                    openWhenHidden: true
                });
            } catch (error) {
                if (isMountedRef.current) {
                    setState(prev => ({ 
                        ...prev, 
                        status: Status.ERROR, 
                        isConnected: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    }));
                }
            }
        };

        startSSE();

        return () => {
            isMountedRef.current = false;
            abortControllerRef.current?.abort();
        };
    }, [sheetId, user?.accessToken, hasEvaluationResult, initialStatus]);

    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    return state;
};
