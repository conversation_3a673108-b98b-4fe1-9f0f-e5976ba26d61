// components/StatusBadge/index.tsx
import React from 'react';
import { Status, StatusUtils } from '../../types/aegisGrader';
import { 
    CheckCircleIcon as CheckCircleSolid,
    ClockIcon,
    ExclamationTriangleIcon,
    ArrowPathIcon
} from '@heroicons/react/24/solid';

interface StatusBadgeProps {
    status: Status | string;
    showText?: boolean;
    size?: 'sm' | 'md' | 'lg';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
    status, 
    showText = true, 
    size = 'md' 
}) => {
    const normalizedStatus = StatusUtils.normalize(status);
    const displayText = StatusUtils.getDisplayText(status);

    const getIcon = () => {
        const iconClass = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';

        switch (normalizedStatus) {
            case Status.COMPLETED:
                return <CheckCircleSolid className={`${iconClass} inline mr-1`} />;
            case Status.PROCESSING:
            case Status.EVALUATING:
                return <ArrowPathIcon className={`${iconClass} inline mr-1 animate-spin`} />;
            case Status.ERROR:
            case Status.PARTIAL_COMPLETION:
                return <ExclamationTriangleIcon className={`${iconClass} inline mr-1`} />;
            case Status.PENDING:
            default:
                return <ClockIcon className={`${iconClass} inline mr-1`} />;
        }
    };

    const getSizeClasses = () => {
        switch (size) {
            case 'sm':
                return 'px-2 py-1 text-xs';
            case 'lg':
                return 'px-4 py-2 text-base';
            case 'md':
            default:
                return 'px-2 sm:px-3 py-1 text-xs sm:text-sm';
        }
    };

    return (
        <div className={`${getSizeClasses()} rounded-full font-medium ${StatusUtils.getStatusClasses(status)}`}>
            {getIcon()}
            {showText && (
                <>
                    <span className="hidden xs:inline sm:inline">{displayText.toUpperCase()}</span>
                    <span className="xs:hidden sm:hidden">{displayText.split(' ')[0].toUpperCase()}</span>
                </>
            )}
        </div>
    );
};
