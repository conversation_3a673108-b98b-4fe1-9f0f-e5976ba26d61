# AegisGrader Refund System Documentation (Manual Refund Approach)

## Overview

The AegisGrader system supports manual refunds for failed grading attempts. Teachers can request refunds for individual failed answer sheets or process bulk refunds for entire submissions.

## Manual Refund Workflow

### 1. Initial Credit Deduction
- When users request presigned URLs for grading, credits are deducted upfront (1 credit per answer sheet)
- Original transaction ID is stored for refund tracking
- AegisGrader record is created when Lambda processes the grading

### 2. Grading Processing
- Lambda processes the grading and creates/updates the AegisGrader document in MongoDB
- Failed evaluations are marked with status 'error'
- No automatic refunds are processed

### 3. Manual Refund Processing
- Teachers can manually request refunds for failed answer sheets
- Individual sheet refunds or bulk submission refunds are supported
- Each refund is tracked at the individual answer sheet level

## Enhanced Manifest Structure

The manifest now includes credit information for Lambda:

```javascript
{
  "testDetails": { ... },
  "files": [ ... ],
  "creditInfo": {
    "userId": "60f1b2b3c4d5e6f7a8b9c0d1",
    "userType": "Teacher",
    "totalCreditsCharged": 5,
    "originalTransactionId": "usage_abc123",
    "creditsPerSheet": 1
  }
}
```

## AegisGrader Model Structure (Created by Lambda)

```javascript
{
  testDetails: { ... },
  answerSheets: [
    {
      id: String,
      studentName: String,
      rollNumber: String,
      pdfUrl: String,
      timestamp: Number,
      className: String,
      evaluationResult: Mixed,
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'error'],
        default: 'pending'
      },
      processedAt: Date
    }
  ],
  processingStats: {
    totalAnswerSheets: Number,
    successfulEvaluations: Number,
    failedEvaluations: Number,
    completedAt: Date,
    processingStartedAt: Date,
    overallStatus: String
  },
  creditInfo: {
    totalCreditsCharged: Number,
    creditsRefunded: Number,
    originalTransactionId: String,
    refundTransactionIds: [String]
  }
}
```

## API Endpoints

### POST /api/aegisGrader/refunds/:submissionId
Manual refund endpoint for teachers to process refunds.

**Request Body (Individual Sheet Refund):**
```javascript
{
  "sheetId": "sheet_001"
}
```

**Request Body (Bulk Submission Refund):**
```javascript
{
  // Empty body for bulk refund of all failed sheets
}
```

**Response:**
```javascript
{
  "message": "Refund processed successfully",
  "success": true,
  "totalRefunded": 2,
  "processedSubmissions": 1,
  "results": [
    {
      "submissionId": "60f1b2b3c4d5e6f7a8b9c0d2",
      "refundAmount": 2,
      "failedSheetsCount": 2
    }
  ]
}
```

### POST /api/aegisGrader/lambda-refunds (Deprecated)
Legacy endpoint - no longer supported.

**Response:**
```javascript
{
  "message": "Lambda refunds are no longer supported. Please use manual refund process.",
  "success": false
}
```

### POST /api/aegisGrader/processing-results (Optional)
Optional endpoint for Lambda to notify completion (for monitoring).

**Request Body:**
```javascript
{
  "submissionId": "...",
  "processingStats": { ... }
}
```

### GET /api/aegisGrader/submissions/:userId
Returns all submissions with processing status and refund information.

## Manual Refund Integration

### Teacher Workflow
1. **View Submissions**: Teachers view their grading submissions in the dashboard
2. **Identify Failed Sheets**: Failed answer sheets are marked with status 'error'
3. **Request Refunds**: Teachers can click "Request Refund" on individual failed sheets
4. **Bulk Refunds**: Teachers can process refunds for all failed sheets in a submission

### Example Manual Refund Calls
```javascript
// Individual sheet refund
const response = await axios.post(`/api/aegisGrader/refunds/${submissionId}`, {
  sheetId: "sheet_001"
});

// Bulk submission refund (all failed sheets)
const response = await axios.post(`/api/aegisGrader/refunds/${submissionId}`, {});
```

## Credit Service Methods

#### `refundCredits(userId, userType, creditAmount, refundDetails)`
Core refund functionality for any failed operations.

#### `processLambdaRefunds(creditInfo, failedSheetCount)` (Deprecated)
Legacy method - no longer used for refunds.

#### `determineUserType(userId)`
Determines if a user is a Student or Teacher by checking both collections.

## Benefits

1. **Simplified Architecture**: Lambda handles everything directly, reducing backend complexity
2. **Automatic Refunds**: Users automatically get credits back for failed processing
3. **Fair Billing**: Users only pay for successful evaluations
4. **Scalable**: Lambda processes refunds without stressing the main server
5. **Audit Trail**: Complete transaction history for credits and refunds

## Key Simplifications

1. **No Backend Document Creation**: AegisGrader documents are created only by Lambda
2. **Credit Info in Manifest**: All necessary credit information is passed to Lambda
3. **Direct Refund API**: Simple endpoint for Lambda to process refunds
4. **Reduced Complexity**: Removed complex backend processing logic
