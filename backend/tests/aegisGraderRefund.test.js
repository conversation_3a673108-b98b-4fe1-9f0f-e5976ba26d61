/**
 * Test script for AegisGrader Refund System
 * This script tests the refund functionality for failed grading attempts
 */

import mongoose from 'mongoose';
import AegisGrader from '../models/AegisGrader.js';
import CreditService from '../services/creditService.js';
import Teacher from '../models/Teacher.js';
import CreditTransaction from '../models/CreditTransaction.js';

// Test configuration
const TEST_CONFIG = {
    mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/aegisscholar-test',
    testUserId: null, // Will be created during test
    testSubmissionId: null // Will be created during test
};

/**
 * Setup test environment
 */
async function setupTest() {
    try {
        // Connect to test database
        await mongoose.connect(TEST_CONFIG.mongoUri);
        console.log('✅ Connected to test database');

        // Create a test teacher
        const testTeacher = new Teacher({
            username: 'test_teacher_refund',
            email: '<EMAIL>',
            password: 'testpassword123',
            name: { first: 'Test', last: 'Teacher' },
            credits: { balance: 100, totalEarned: 100, totalSpent: 0 }
        });

        await testTeacher.save();
        TEST_CONFIG.testUserId = testTeacher._id.toString();
        console.log('✅ Created test teacher with ID:', TEST_CONFIG.testUserId);

        return true;
    } catch (error) {
        console.error('❌ Setup failed:', error);
        return false;
    }
}

/**
 * Test creating a submission with failed answer sheets
 */
async function testCreateSubmissionWithFailures() {
    try {
        console.log('\n🧪 Testing submission creation with failures...');

        const submission = new AegisGrader({
            testDetails: {
                createdBy: TEST_CONFIG.testUserId,
                className: 'Test Class 10',
                subject: 'Mathematics',
                date: '2025-01-15'
            },
            answerSheets: [
                {
                    id: 'student1.pdf',
                    studentName: 'John Doe',
                    rollNumber: '001',
                    pdfUrl: 'https://example.com/student1.pdf',
                    timestamp: Date.now(),
                    className: 'Test Class 10',
                    status: 'completed',
                    evaluationResult: 'Grade: A+, Score: 95/100',
                    processedAt: new Date()
                },
                {
                    id: 'student2.pdf',
                    studentName: 'Jane Smith',
                    rollNumber: '002',
                    pdfUrl: 'https://example.com/student2.pdf',
                    timestamp: Date.now(),
                    className: 'Test Class 10',
                    status: 'error',
                    evaluationResult: 'Error: processing answer sheet, please try again',
                    processedAt: new Date(),
                    errorMessage: 'PDF parsing failed'
                },
                {
                    id: 'student3.pdf',
                    studentName: 'Bob Johnson',
                    rollNumber: '003',
                    pdfUrl: 'https://example.com/student3.pdf',
                    timestamp: Date.now(),
                    className: 'Test Class 10',
                    status: 'error',
                    evaluationResult: 'Error: processing answer sheet, please try again',
                    processedAt: new Date(),
                    errorMessage: 'AI processing timeout'
                }
            ],
            processingStats: {
                totalAnswerSheets: 3,
                successfulEvaluations: 1,
                failedEvaluations: 2,
                completedAt: new Date(),
                processingStartedAt: new Date(Date.now() - 300000), // 5 minutes ago
                overallStatus: 'partial_completion'
            },
            creditInfo: {
                totalCreditsCharged: 3,
                creditsRefunded: 0,
                originalTransactionId: 'usage_test_12345',
                refundTransactionIds: []
            },
            questionPaper: {
                type: 'questionPaper',
                pdfUrl: 'https://example.com/question.pdf',
                timestamp: Date.now()
            },
            rubric: {
                type: 'rubric',
                pdfUrl: 'https://example.com/rubric.pdf',
                timestamp: Date.now()
            }
        });

        await submission.save();
        TEST_CONFIG.testSubmissionId = submission._id.toString();
        console.log('✅ Created test submission with ID:', TEST_CONFIG.testSubmissionId);
        console.log('📊 Submission stats:', {
            total: submission.processingStats.totalAnswerSheets,
            successful: submission.processingStats.successfulEvaluations,
            failed: submission.processingStats.failedEvaluations
        });

        return submission;
    } catch (error) {
        console.error('❌ Failed to create test submission:', error);
        throw error;
    }
}

/**
 * Test the refund processing
 */
async function testRefundProcessing() {
    try {
        console.log('\n🧪 Testing refund processing...');

        // Get the submission
        const submission = await AegisGrader.findById(TEST_CONFIG.testSubmissionId);
        if (!submission) {
            throw new Error('Test submission not found');
        }

        // Check initial credit balance
        const initialBalance = await CreditService.getCreditBalance(TEST_CONFIG.testUserId, 'Teacher');
        console.log('💰 Initial credit balance:', initialBalance);

        // Process refunds using the simplified Lambda approach
        const failedSheets = submission.answerSheets.filter(sheet => sheet.status === 'error');
        const creditInfo = {
            userId: TEST_CONFIG.testUserId,
            userType: 'Teacher',
            originalTransactionId: submission.creditInfo.originalTransactionId
        };

        const refundResult = await CreditService.processLambdaRefunds(creditInfo, failedSheets.length);
        console.log('🔄 Refund processing result:', refundResult);

        // Check final credit balance
        const finalBalance = await CreditService.getCreditBalance(TEST_CONFIG.testUserId, 'Teacher');
        console.log('💰 Final credit balance:', finalBalance);

        // Verify refund amount
        const expectedRefund = 2; // 2 failed sheets
        if (refundResult.refundAmount === expectedRefund) {
            console.log('✅ Correct refund amount:', expectedRefund);
        } else {
            console.log('❌ Incorrect refund amount. Expected:', expectedRefund, 'Got:', refundResult.refundAmount);
        }

        // Verify balance increase
        const balanceIncrease = finalBalance - initialBalance;
        if (balanceIncrease === expectedRefund) {
            console.log('✅ Credit balance increased correctly by:', balanceIncrease);
        } else {
            console.log('❌ Credit balance increase incorrect. Expected:', expectedRefund, 'Got:', balanceIncrease);
        }

        // Check transaction record
        const refundTransactions = await CreditTransaction.find({
            userId: TEST_CONFIG.testUserId,
            type: 'REFUND',
            'usage.relatedId': TEST_CONFIG.testSubmissionId
        });

        if (refundTransactions.length > 0) {
            console.log('✅ Refund transaction created:', refundTransactions[0].transactionId);
        } else {
            console.log('❌ No refund transaction found');
        }

        return refundResult;
    } catch (error) {
        console.error('❌ Refund processing test failed:', error);
        throw error;
    }
}

/**
 * Test duplicate refund prevention
 */
async function testDuplicateRefundPrevention() {
    try {
        console.log('\n🧪 Testing duplicate refund prevention...');

        const submission = await AegisGrader.findById(TEST_CONFIG.testSubmissionId);
        const balanceBeforeSecondRefund = await CreditService.getCreditBalance(TEST_CONFIG.testUserId, 'Teacher');

        // Try to process refunds again (should return 0 since already processed)
        const failedSheets = submission.answerSheets.filter(sheet => sheet.status === 'error');
        const creditInfo = {
            userId: TEST_CONFIG.testUserId,
            userType: 'Teacher',
            originalTransactionId: submission.creditInfo.originalTransactionId
        };

        // This should return 0 refund amount since we already processed refunds
        const secondRefundResult = await CreditService.processLambdaRefunds(creditInfo, 0); // Pass 0 to simulate no new failures
        console.log('🔄 Second refund attempt result:', secondRefundResult);

        const balanceAfterSecondRefund = await CreditService.getCreditBalance(TEST_CONFIG.testUserId, 'Teacher');

        // Balance should not change
        if (balanceBeforeSecondRefund === balanceAfterSecondRefund) {
            console.log('✅ Duplicate refund prevented - balance unchanged');
        } else {
            console.log('❌ Duplicate refund not prevented - balance changed');
        }

        if (secondRefundResult.refundAmount === 0) {
            console.log('✅ Second refund correctly returned 0 amount');
        } else {
            console.log('❌ Second refund incorrectly processed amount:', secondRefundResult.refundAmount);
        }

    } catch (error) {
        console.error('❌ Duplicate refund prevention test failed:', error);
        throw error;
    }
}

/**
 * Cleanup test data
 */
async function cleanup() {
    try {
        console.log('\n🧹 Cleaning up test data...');

        // Remove test submission
        if (TEST_CONFIG.testSubmissionId) {
            await AegisGrader.findByIdAndDelete(TEST_CONFIG.testSubmissionId);
            console.log('✅ Removed test submission');
        }

        // Remove test teacher
        if (TEST_CONFIG.testUserId) {
            await Teacher.findByIdAndDelete(TEST_CONFIG.testUserId);
            console.log('✅ Removed test teacher');
        }

        // Remove test transactions
        await CreditTransaction.deleteMany({
            userId: TEST_CONFIG.testUserId
        });
        console.log('✅ Removed test transactions');

        // Close database connection
        await mongoose.connection.close();
        console.log('✅ Database connection closed');

    } catch (error) {
        console.error('❌ Cleanup failed:', error);
    }
}

/**
 * Run all tests
 */
async function runTests() {
    console.log('🚀 Starting AegisGrader Refund System Tests\n');

    try {
        // Setup
        const setupSuccess = await setupTest();
        if (!setupSuccess) {
            console.log('❌ Setup failed, aborting tests');
            return;
        }

        // Run tests
        await testCreateSubmissionWithFailures();
        await testRefundProcessing();
        await testDuplicateRefundPrevention();

        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.error('\n💥 Test suite failed:', error);
    } finally {
        await cleanup();
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests();
}

export { runTests, setupTest, testCreateSubmissionWithFailures, testRefundProcessing, cleanup };
